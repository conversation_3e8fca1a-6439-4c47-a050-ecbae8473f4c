<?php

namespace App\Entity;

use App\Repository\VideoRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: VideoRepository::class)]
class Video
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank(message: 'Title is required')]
    #[Assert\Length(max: 255, maxMessage: 'Title cannot be longer than 255 characters')]
    private ?string $title = null;



    #[ORM\Column(length: 100, nullable: true)]
    #[Assert\Length(max: 100, maxMessage: 'Category cannot be longer than 100 characters')]
    private ?string $category = null;

    #[ORM\Column]
    private bool $isFree = false;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\Length(max: 255, maxMessage: 'Thumbnail path cannot be longer than 255 characters')]
    private ?string $thumbnail = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\Length(max: 255, maxMessage: 'Video file path cannot be longer than 255 characters')]
    private ?string $videoFile = null;



    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Assert\Length(max: 2000, maxMessage: 'Description cannot be longer than 2000 characters')]
    private ?string $description = null;



    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $updatedAt = null;

    #[ORM\Column]
    private bool $isActive = true;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    #[Assert\PositiveOrZero(message: 'Price must be a positive number')]
    private ?string $price = null;

    #[ORM\Column(length: 20)]
    #[Assert\NotBlank(message: 'Video source type is required')]
    #[Assert\Choice(choices: ['youtube', 'vdocipher', 'upload'], message: 'Invalid video source type')]
    private string $videoSourceType = 'upload';

    #[ORM\Column(length: 500, nullable: true)]
    #[Assert\Url(message: 'Please enter a valid YouTube URL')]
    private ?string $youtubeUrl = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\Length(max: 255, maxMessage: 'VdoCipher video ID cannot be longer than 255 characters')]
    private ?string $vdocipherVideoId = null;

    #[ORM\Column(length: 30)]
    #[Assert\NotBlank(message: 'Access level is required')]
    #[Assert\Choice(choices: ['public_free', 'login_required_free', 'premium'], message: 'Invalid access level')]
    private string $accessLevel = 'public_free';

    #[ORM\Column(nullable: true)]
    #[Assert\PositiveOrZero(message: 'Access duration must be a positive number')]
    private ?int $accessDuration = null; // Duration in days for premium videos



    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }



    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(?string $category): static
    {
        $this->category = $category;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function isFree(): bool
    {
        // For backward compatibility, check both old and new system
        return $this->isFree || $this->isFreeVideo();
    }

    public function setIsFree(bool $isFree): static
    {
        $this->isFree = $isFree;

        // Update access level based on isFree for backward compatibility
        if ($isFree && $this->accessLevel === 'premium') {
            $this->accessLevel = 'public_free';
        } elseif (!$isFree && $this->accessLevel !== 'premium') {
            $this->accessLevel = 'premium';
        }

        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getThumbnail(): ?string
    {
        return $this->thumbnail;
    }

    public function setThumbnail(?string $thumbnail): static
    {
        $this->thumbnail = $thumbnail;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getVideoFile(): ?string
    {
        return $this->videoFile;
    }

    public function setVideoFile(?string $videoFile): static
    {
        $this->videoFile = $videoFile;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }



    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }



    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): static
    {
        $this->isActive = $isActive;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getPrice(): ?string
    {
        return $this->price;
    }

    public function setPrice(?string $price): static
    {
        $this->price = $price;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }



    public function getFormattedPrice(): string
    {
        if ($this->price === null || $this->price === '0.00') {
            return 'Free';
        }
        return '$' . number_format((float)$this->price, 2);
    }

    public function getVideoSourceType(): string
    {
        return $this->videoSourceType;
    }

    public function setVideoSourceType(string $videoSourceType): static
    {
        $this->videoSourceType = $videoSourceType;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getYoutubeUrl(): ?string
    {
        return $this->youtubeUrl;
    }

    public function setYoutubeUrl(?string $youtubeUrl): static
    {
        $this->youtubeUrl = $youtubeUrl;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getVdocipherVideoId(): ?string
    {
        return $this->vdocipherVideoId;
    }

    public function setVdocipherVideoId(?string $vdocipherVideoId): static
    {
        $this->vdocipherVideoId = $vdocipherVideoId;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getAccessLevel(): string
    {
        return $this->accessLevel;
    }

    public function setAccessLevel(string $accessLevel): static
    {
        $this->accessLevel = $accessLevel;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getAccessDuration(): ?int
    {
        return $this->accessDuration;
    }

    public function setAccessDuration(?int $accessDuration): static
    {
        $this->accessDuration = $accessDuration;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    /**
     * Check if video is free based on access level
     */
    public function isFreeVideo(): bool
    {
        return in_array($this->accessLevel, ['public_free', 'login_required_free']);
    }

    /**
     * Check if video requires login
     */
    public function requiresLogin(): bool
    {
        return in_array($this->accessLevel, ['login_required_free', 'premium']);
    }

    /**
     * Check if video is premium (requires payment)
     */
    public function isPremium(): bool
    {
        return $this->accessLevel === 'premium';
    }

    /**
     * Get formatted access duration
     */
    public function getFormattedAccessDuration(): string
    {
        if (!$this->accessDuration) {
            return 'Lifetime';
        }

        if ($this->accessDuration === 1) {
            return '1 day';
        } elseif ($this->accessDuration < 30) {
            return $this->accessDuration . ' days';
        } elseif ($this->accessDuration === 30) {
            return '1 month';
        } elseif ($this->accessDuration === 365) {
            return '1 year';
        } else {
            return $this->accessDuration . ' days';
        }
    }

    /**
     * Extract YouTube video ID from URL
     */
    public function getYoutubeVideoId(): ?string
    {
        if (!$this->youtubeUrl) {
            return null;
        }

        // Handle various YouTube URL formats
        $patterns = [
            '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/',
            '/youtube\.com\/v\/([a-zA-Z0-9_-]{11})/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $this->youtubeUrl, $matches)) {
                return $matches[1];
            }
        }

        return null;
    }

    /**
     * Get YouTube embed URL
     */
    public function getYoutubeEmbedUrl(): ?string
    {
        $videoId = $this->getYoutubeVideoId();
        if (!$videoId) {
            return null;
        }

        return "https://www.youtube.com/embed/{$videoId}?rel=0&modestbranding=1&showinfo=0";
    }


}
