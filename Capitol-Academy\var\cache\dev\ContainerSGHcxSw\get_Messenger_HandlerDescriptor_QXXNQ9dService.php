<?php

namespace ContainerSGHcxSw;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_Messenger_HandlerDescriptor_QXXNQ9dService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.messenger.handler_descriptor.QXXNQ9d' shared service.
     *
     * @return \Symfony\Component\Messenger\Handler\HandlerDescriptor
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'messenger'.\DIRECTORY_SEPARATOR.'Handler'.\DIRECTORY_SEPARATOR.'HandlerDescriptor.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'process'.\DIRECTORY_SEPARATOR.'Messenger'.\DIRECTORY_SEPARATOR.'RunProcessMessageHandler.php';

        return $container->privates['.messenger.handler_descriptor.QXXNQ9d'] = new \Symfony\Component\Messenger\Handler\HandlerDescriptor(new \Symfony\Component\Process\Messenger\RunProcessMessageHandler(), []);
    }
}
