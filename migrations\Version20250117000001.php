<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Enhanced Video Management System Migration
 * Adds new fields for video source types, access levels, and duration management
 */
final class Version20250117000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add enhanced video management fields: video_source_type, youtube_url, vdocipher_video_id, access_level, access_duration';
    }

    public function up(Schema $schema): void
    {
        // Check if columns already exist before adding them
        $this->addSql('ALTER TABLE video ADD COLUMN IF NOT EXISTS video_source_type VARCHAR(20) NOT NULL DEFAULT \'upload\'');
        $this->addSql('ALTER TABLE video ADD COLUMN IF NOT EXISTS youtube_url VARCHAR(500) DEFAULT NULL');
        $this->addSql('ALTER TABLE video ADD COLUMN IF NOT EXISTS vdocipher_video_id VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE video ADD COLUMN IF NOT EXISTS access_level VARCHAR(30) NOT NULL DEFAULT \'public_free\'');
        $this->addSql('ALTER TABLE video ADD COLUMN IF NOT EXISTS access_duration INT DEFAULT NULL');

        // Update existing videos to have proper access levels based on isFree field
        $this->addSql('UPDATE video SET access_level = \'public_free\' WHERE is_free = 1 AND access_level = \'public_free\'');
        $this->addSql('UPDATE video SET access_level = \'premium\' WHERE is_free = 0 AND access_level = \'public_free\'');
    }

    public function down(Schema $schema): void
    {
        // Remove columns if they exist
        $this->addSql('ALTER TABLE video DROP COLUMN IF EXISTS video_source_type');
        $this->addSql('ALTER TABLE video DROP COLUMN IF EXISTS youtube_url');
        $this->addSql('ALTER TABLE video DROP COLUMN IF EXISTS vdocipher_video_id');
        $this->addSql('ALTER TABLE video DROP COLUMN IF EXISTS access_level');
        $this->addSql('ALTER TABLE video DROP COLUMN IF EXISTS access_duration');
    }
}
