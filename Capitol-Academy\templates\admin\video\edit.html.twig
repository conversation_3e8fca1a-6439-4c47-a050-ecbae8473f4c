{% extends 'admin/base.html.twig' %}

{% block title %}Edit Video - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Video{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_video_index') }}">Videos</a></li>
<li class="breadcrumb-item active">Edit {{ video.title }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">


    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-video mr-3" style="font-size: 2rem;"></i>
                        Edit Video: {{ video.title }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Back to Videos Button -->
                        <a href="{{ path('admin_video_index') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Videos
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method="post" class="needs-validation" enctype="multipart/form-data" novalidate>
            <input type="hidden" name="_token" value="{{ csrf_token('video_edit') }}">
            <div class="card-body">
                    <!-- Single Column Layout -->
                    <div class="row">
                        <div class="col-12">
                            <!-- Video Title and Category Row -->
                            <div class="row">
                                <!-- Video Title -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="title" class="form-label">
                                            <i class="fas fa-video text-primary mr-1" aria-hidden="true"></i>
                                            Video Title <span class="text-danger" aria-label="required">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               id="title"
                                               name="title"
                                               value="{{ video.title }}"
                                               placeholder="Enter video title"
                                               required
                                               maxlength="255"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                        <div class="invalid-feedback">
                                            Please provide a video title.
                                        </div>
                                    </div>
                                </div>

                                <!-- Category -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="category" class="form-label" style="font-weight: normal;">
                                            <i class="fas fa-folder text-primary mr-1" aria-hidden="true"></i>
                                            Category <span class="text-danger" aria-label="required">*</span>
                                        </label>
                                        <select class="form-select enhanced-dropdown"
                                                id="category"
                                                name="category"
                                                required
                                                aria-describedby="category_help category_error"
                                                aria-label="Select a video category"
                                                style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;">
                                            <option value="">Choose a category...</option>
                                            {% for category in categories %}
                                                <option value="{{ category.name }}" {{ video.category == category.name ? 'selected' : '' }}>{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                        <div id="category_help" class="form-text text-muted" style="display: none;">
                                            Select the category that best describes this video. Use arrow keys to navigate options.
                                        </div>
                                        <div id="category_error" class="invalid-feedback" role="alert" aria-live="polite">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Video Source Type and Access Level Row -->
                            <div class="row">
                                <!-- Video Source Type -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="video_source_type" class="form-label">
                                            <i class="fas fa-source-branch text-primary mr-1" aria-hidden="true"></i>
                                            Video Source Type <span class="text-danger" aria-label="required">*</span>
                                        </label>
                                        <select class="form-select enhanced-dropdown"
                                                id="video_source_type"
                                                name="video_source_type"
                                                required
                                                aria-describedby="video_source_type_help video_source_type_error"
                                                aria-label="Select a video source type"
                                                style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;">
                                            <option value="upload" {{ video.videoSourceType == 'upload' ? 'selected' : '' }}>Direct Upload</option>
                                            <option value="youtube" {{ video.videoSourceType == 'youtube' ? 'selected' : '' }}>YouTube Video</option>
                                            <option value="vdocipher" {{ video.videoSourceType == 'vdocipher' ? 'selected' : '' }}>VdoCipher (DRM Protected)</option>
                                        </select>
                                        <small class="form-text text-muted">Choose how you want to provide the video content</small>
                                        <div class="invalid-feedback">
                                            Please select a video source type.
                                        </div>
                                    </div>
                                </div>

                                <!-- Access Level -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="access_level" class="form-label">
                                            <i class="fas fa-shield-alt text-primary mr-1" aria-hidden="true"></i>
                                            Access Level <span class="text-danger" aria-label="required">*</span>
                                        </label>
                                        <select class="form-select enhanced-field"
                                                id="access_level"
                                                name="access_level"
                                                required
                                                style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                            <option value="public_free" {{ video.accessLevel == 'public_free' ? 'selected' : '' }}>Public Free (No login required)</option>
                                            <option value="login_required_free" {{ video.accessLevel == 'login_required_free' ? 'selected' : '' }}>Login Required Free (Registered users only)</option>
                                            <option value="premium" {{ video.accessLevel == 'premium' ? 'selected' : '' }}>Premium (Paid access)</option>
                                        </select>
                                        <small class="form-text text-muted">Choose who can access this video</small>
                                        <div class="invalid-feedback">
                                            Please select an access level.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Video Source Fields (Dynamic) -->
                            <!-- YouTube URL Field -->
                            <div class="form-group video-source-field" data-source="youtube" style="display: {{ video.videoSourceType == 'youtube' ? 'block' : 'none' }};">
                                <label for="youtube_url" class="form-label">
                                    <i class="fab fa-youtube text-danger mr-1" aria-hidden="true"></i>
                                    YouTube URL
                                </label>
                                <input type="url"
                                       class="form-control enhanced-field"
                                       id="youtube_url"
                                       name="youtube_url"
                                       value="{{ video.youtubeUrl }}"
                                       placeholder="https://www.youtube.com/watch?v=..."
                                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                <small class="form-text text-muted">Enter the full YouTube video URL</small>
                                <div class="invalid-feedback">
                                    Please provide a valid YouTube URL.
                                </div>
                            </div>

                            <!-- VdoCipher Video ID Field -->
                            <div class="form-group video-source-field" data-source="vdocipher" style="display: {{ video.videoSourceType == 'vdocipher' ? 'block' : 'none' }};">
                                <label for="vdocipher_video_id" class="form-label">
                                    <i class="fas fa-shield-alt text-success mr-1" aria-hidden="true"></i>
                                    VdoCipher Video ID
                                </label>
                                <input type="text"
                                       class="form-control enhanced-field"
                                       id="vdocipher_video_id"
                                       name="vdocipher_video_id"
                                       value="{{ video.vdocipherVideoId }}"
                                       placeholder="Enter VdoCipher video ID"
                                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                <small class="form-text text-muted">Enter the VdoCipher video ID for DRM-protected streaming</small>
                                <div class="invalid-feedback">
                                    Please provide a valid VdoCipher video ID.
                                </div>
                            </div>

                            <!-- Premium Video Settings -->
                            <div class="premium-settings" style="display: {{ video.accessLevel == 'premium' ? 'block' : 'none' }};">
                                <div class="row">
                                    <!-- Price -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="price" class="form-label">
                                                <i class="fas fa-dollar-sign text-success mr-1" aria-hidden="true"></i>
                                                Price (USD)
                                            </label>
                                            <input type="number"
                                                   class="form-control enhanced-field"
                                                   id="price"
                                                   name="price"
                                                   value="{{ video.price }}"
                                                   placeholder="0.00"
                                                   min="0"
                                                   step="0.01"
                                                   style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                            <small class="form-text text-muted">Set price for premium videos</small>
                                            <div class="invalid-feedback">
                                                Please provide a valid price.
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Access Duration -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="access_duration" class="form-label">
                                                <i class="fas fa-clock text-warning mr-1" aria-hidden="true"></i>
                                                Access Duration (Days)
                                            </label>
                                            <input type="number"
                                                   class="form-control enhanced-field"
                                                   id="access_duration"
                                                   name="access_duration"
                                                   value="{{ video.accessDuration }}"
                                                   placeholder="Leave empty for lifetime access"
                                                   min="1"
                                                   style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                            <small class="form-text text-muted">Number of days user will have access after purchase (leave empty for lifetime access)</small>
                                            <div class="invalid-feedback">
                                                Please provide a valid access duration.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Video Description -->
                            <div class="form-group">
                                <label for="description" class="form-label" style="font-weight: normal;">
                                    <i class="fas fa-align-left text-primary mr-1" aria-hidden="true"></i>
                                    Video Description <span class="text-danger" aria-label="required">*</span>
                                </label>
                                <textarea class="form-control enhanced-field"
                                          id="description"
                                          name="description"
                                          rows="4"
                                          placeholder="Enter video description"
                                          required
                                          maxlength="2000"
                                          style="border: 2px solid #ced4da;">{{ video.description }}</textarea>
                                <div class="invalid-feedback">
                                    Please provide a video description.
                                </div>
                                <small class="form-text text-muted help-text" style="display: none;">
                                    Provide a comprehensive description of the video content.
                                </small>
                            </div>

                            <!-- Video File Upload (Only for Direct Upload) -->
                            <div class="form-group video-source-field" data-source="upload" style="display: {{ video.videoSourceType == 'upload' ? 'block' : 'none' }};">
                                <label for="video_file" class="form-label" style="font-weight: normal;">
                                    <i class="fas fa-file-video text-primary mr-1" aria-hidden="true"></i>
                                    Video File
                                </label>
                                <input type="file"
                                       class="form-control enhanced-file-field"
                                       id="video_file"
                                       name="video_file"
                                       accept="video/*"
                                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                <small class="form-text text-muted help-text" style="display: none;">
                                    Upload a video file (MP4, AVI, MOV, WMV, WebM). Max size: 500MB. Leave empty to keep current video.
                                </small>

                                <!-- Current Video Display -->
                                {% if video.videoFile %}
                                <div class="current-video mt-3 d-flex flex-column align-items-center">
                                    <div class="professional-video-container" style="width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;">
                                        <video controls class="w-100 h-100" style="object-fit: cover;">
                                            <source src="/uploads/videos/files/{{ video.videoFile }}" type="video/mp4">
                                            Your browser does not support the video tag.
                                        </video>
                                    </div>
                                    <small class="form-text text-info d-block mt-2 text-center">Current Video (600x300px)</small>
                                </div>
                                {% endif %}



                                <div class="invalid-feedback">
                                    Please select a video file.
                                </div>
                            </div>

                            <!-- Thumbnail Upload -->
                            <div class="form-group">
                                <label for="thumbnail_file" class="form-label" style="font-weight: normal;">
                                    <i class="fas fa-image text-primary mr-1" aria-hidden="true"></i>
                                    Video Thumbnail
                                </label>
                                <input type="file"
                                       class="form-control enhanced-file-field"
                                       id="thumbnail_file"
                                       name="thumbnail_file"
                                       accept="image/*"
                                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                <small class="form-text text-muted help-text" style="display: none;">
                                    Upload a thumbnail image (JPEG, PNG, WebP). Recommended size: 300x200px. Leave empty to keep current thumbnail.
                                </small>

                                <!-- Current Thumbnail Display -->
                                {% if video.thumbnail %}
                                <div class="current-thumbnail mt-3 d-flex flex-column align-items-center">
                                    <div class="professional-image-container" style="width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;">
                                        <img src="/uploads/videos/thumbnails/{{ video.thumbnail }}" alt="Current Thumbnail" class="w-100 h-100" style="object-fit: cover;">
                                    </div>
                                    <small class="form-text text-info d-block mt-2 text-center">Current Thumbnail (300x200px)</small>
                                </div>
                                {% endif %}



                                <div class="invalid-feedback">
                                    Please select a thumbnail image.
                                </div>
                            </div>





                            <!-- Hidden Fields -->
                            <input type="hidden" id="is_free" name="is_free" value="{{ video.isFree ? '1' : '0' }}">

                        </div>
                    </div>
                </div>

                <div class="card-footer" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 1px solid #dee2e6; padding: 1.5rem;">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-lg" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem; transition: all 0.3s ease;">
                                <i class="fas fa-save mr-2"></i>
                                Update Video
                            </button>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ path('admin_video_index') }}" class="btn btn-secondary btn-lg" style="font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem;">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Dynamic form field handling
    function toggleVideoSourceFields() {
        const sourceType = $('#video_source_type').val();

        // Hide all source-specific fields
        $('.video-source-field').hide();

        // Show relevant field based on source type
        $(`.video-source-field[data-source="${sourceType}"]`).show();

        // Update required attributes
        $('.video-source-field input').removeAttr('required');
        $(`.video-source-field[data-source="${sourceType}"] input`).attr('required', 'required');
    }

    function toggleAccessLevelFields() {
        const accessLevel = $('#access_level').val();

        if (accessLevel === 'premium') {
            $('.premium-settings').show();
            $('#price').attr('required', 'required');
        } else {
            $('.premium-settings').hide();
            $('#price').removeAttr('required');
            $('#access_duration').removeAttr('required');
        }
    }

    // Initialize form state
    toggleVideoSourceFields();
    toggleAccessLevelFields();

    // Handle video source type changes
    $('#video_source_type').on('change', function() {
        toggleVideoSourceFields();
    });

    // Handle access level changes
    $('#access_level').on('change', function() {
        toggleAccessLevelFields();
    });

    // YouTube URL validation
    $('#youtube_url').on('blur', function() {
        const url = $(this).val();
        if (url && !isValidYouTubeUrl(url)) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').text('Please enter a valid YouTube URL');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    function isValidYouTubeUrl(url) {
        const patterns = [
            /^https?:\/\/(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/,
            /^https?:\/\/(www\.)?youtube\.com\/v\/([a-zA-Z0-9_-]{11})/
        ];
        return patterns.some(pattern => pattern.test(url));
    }

    // Price formatting
    $('#price').on('blur', function() {
        const value = parseFloat($(this).val());
        if (!isNaN(value)) {
            $(this).val(value.toFixed(2));
        }
    });

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type="submit"]');

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Update Video';
                        }

                        // Show help text when validation fails
                        $('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating Video...';
                        }

                        // Hide help text when form is valid
                        $('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Pricing radio button logic
    const pricingRadios = document.querySelectorAll('input[name="pricing_type"]');
    const priceInputGroup = document.getElementById('price-input-group');
    const priceInput = document.getElementById('price');
    const isFreeInput = document.getElementById('is_free');

    function updatePricingDisplay() {
        const selectedValue = document.querySelector('input[name="pricing_type"]:checked').value;
        if (selectedValue === 'free') {
            priceInputGroup.style.display = 'none';
            priceInput.value = '0.00';
            isFreeInput.value = '1';
            priceInput.removeAttribute('required');
        } else if (selectedValue === 'premium') {
            priceInputGroup.style.display = 'block';
            priceInput.focus();
            isFreeInput.value = '0';
            priceInput.setAttribute('required', 'required');
        }
    }

    // Add event listeners to radio buttons
    pricingRadios.forEach(radio => {
        radio.addEventListener('change', updatePricingDisplay);
    });

    // Initialize state
    updatePricingDisplay();

    // Video file preview functionality
    const videoFileInput = document.getElementById('video_file');
    if (videoFileInput) {
        videoFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const currentVideoContainer = document.querySelector('.current-video');

            if (file && file.type.startsWith('video/')) {
                const url = URL.createObjectURL(file);

                // Update the current video display with new file
                if (currentVideoContainer) {
                    const videoElement = currentVideoContainer.querySelector('video');
                    if (videoElement) {
                        videoElement.src = url;
                    }

                    // Update the label to show it's a new video
                    const label = currentVideoContainer.querySelector('small');
                    if (label) {
                        const fileSize = (file.size / 1024 / 1024).toFixed(2);
                        label.innerHTML = `New Video Selected: ${file.name} (${fileSize} MB)`;
                        label.className = 'form-text text-success d-block mt-2 text-center';
                    }
                } else {
                    // Create new video preview if no current video exists
                    const videoContainer = document.createElement('div');
                    videoContainer.className = 'current-video mt-3 d-flex flex-column align-items-center';
                    videoContainer.innerHTML = `
                        <div class="professional-video-container" style="width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;">
                            <video controls class="w-100 h-100" style="object-fit: cover;" src="${url}">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                        <small class="form-text text-success d-block mt-2 text-center">New Video Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)</small>
                    `;
                    videoFileInput.parentNode.appendChild(videoContainer);
                }
            }
        });
    }

    // Thumbnail preview functionality
    const thumbnailFileInput = document.getElementById('thumbnail_file');
    if (thumbnailFileInput) {
        thumbnailFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const currentThumbnailContainer = document.querySelector('.current-thumbnail');

            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Update the current thumbnail display with new file
                    if (currentThumbnailContainer) {
                        const imgElement = currentThumbnailContainer.querySelector('img');
                        if (imgElement) {
                            imgElement.src = e.target.result;
                        }

                        // Update the label to show it's a new thumbnail
                        const label = currentThumbnailContainer.querySelector('small');
                        if (label) {
                            label.innerHTML = `New Thumbnail Selected: ${file.name}`;
                            label.className = 'form-text text-success d-block mt-2 text-center';
                        }
                    } else {
                        // Create new thumbnail preview if no current thumbnail exists
                        const thumbnailContainer = document.createElement('div');
                        thumbnailContainer.className = 'current-thumbnail mt-3 d-flex flex-column align-items-center';
                        thumbnailContainer.innerHTML = `
                            <div class="professional-image-container" style="width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;">
                                <img src="${e.target.result}" alt="Thumbnail Preview" class="w-100 h-100" style="object-fit: cover;">
                            </div>
                            <small class="form-text text-success d-block mt-2 text-center">New Thumbnail Selected: ${file.name}</small>
                        `;
                        thumbnailFileInput.parentNode.appendChild(thumbnailContainer);
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Enhanced category selection with search functionality
    const categorySelect = document.getElementById('category');
    if (categorySelect && typeof $ !== 'undefined') {
        $(categorySelect).select2({
            placeholder: 'Search and select a category...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }
});
</script>
{% endblock %}
