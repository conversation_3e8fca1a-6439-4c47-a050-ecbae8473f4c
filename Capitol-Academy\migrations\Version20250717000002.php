<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add duration and price columns to course_module table
 */
final class Version20250717000002 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add duration and price columns to course_module table';
    }

    public function up(Schema $schema): void
    {
        // Add duration column (nullable integer for duration in minutes)
        $this->addSql('ALTER TABLE course_module ADD COLUMN duration INT NULL');
        
        // Add price column (nullable decimal for module price)
        $this->addSql('ALTER TABLE course_module ADD COLUMN price DECIMAL(10,2) NULL DEFAULT \'0.00\'');
    }

    public function down(Schema $schema): void
    {
        // Remove the added columns
        $this->addSql('ALTER TABLE course_module DROP COLUMN duration');
        $this->addSql('ALTER TABLE course_module DROP COLUMN price');
    }
}
