<?php

namespace ContainerSGHcxSw;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_C34BrisService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.c34Bris' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.c34Bris'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'entityManager' => ['services', 'doctrine.orm.default_entity_manager', 'getDoctrine_Orm_DefaultEntityManagerService', false],
            'instructorRepository' => ['privates', 'App\\Repository\\InstructorRepository', 'getInstructorRepositoryService', true],
        ], [
            'entityManager' => '?',
            'instructorRepository' => 'App\\Repository\\InstructorRepository',
        ]);
    }
}
