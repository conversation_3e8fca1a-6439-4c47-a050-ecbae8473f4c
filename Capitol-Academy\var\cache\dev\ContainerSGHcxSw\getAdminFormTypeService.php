<?php

namespace ContainerSGHcxSw;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getAdminFormTypeService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Form\AdminFormType' shared autowired service.
     *
     * @return \App\Form\AdminFormType
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'form'.\DIRECTORY_SEPARATOR.'FormTypeInterface.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'form'.\DIRECTORY_SEPARATOR.'AbstractType.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Form'.\DIRECTORY_SEPARATOR.'AdminFormType.php';

        return $container->privates['App\\Form\\AdminFormType'] = new \App\Form\AdminFormType(($container->privates['App\\Service\\EmailUniquenessValidator'] ?? $container->load('getEmailUniquenessValidatorService')), ($container->privates['App\\Service\\ValidationService'] ?? $container->load('getValidationServiceService')));
    }
}
