<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/video/create.html.twig */
class __TwigTemplate_f66e7539f678039f109752d46f2f82b9 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/video/create.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/video/create.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Create Video - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Create New Video";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_index");
        yield "\">Videos</a></li>
<li class=\"breadcrumb-item active\">Create Video</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-video mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Video
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Videos Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_index");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Videos
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("video_create"), "html", null, true);
        yield "\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Video Title and Category Row -->
                            <div class=\"row\">
                                <!-- Video Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-video text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Video Title <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               placeholder=\"Enter video title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a video title.
                                        </div>
                                    </div>
                                </div>

                                <!-- Category -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"category\" class=\"form-label\">
                                            <i class=\"fas fa-folder text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Category <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"category\"
                                                name=\"category\"
                                                required
                                                aria-describedby=\"category_help category_error\"
                                                aria-label=\"Select a video category\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                            <option value=\"\">Choose a category...</option>
                                            ";
        // line 100
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 100, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 101
            yield "                                                <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 101), "html", null, true);
            yield "\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 101), "html", null, true);
            yield "</option>
                                            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['category'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 103
        yield "                                        </select>
                                        <div id=\"category_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                            Select the category that best describes this video. Use arrow keys to navigate options.
                                        </div>
                                        <div id=\"category_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Video Source Type and Access Level Row -->
                            <div class=\"row\">
                                <!-- Video Source Type -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"video_source_type\" class=\"form-label\">
                                            <i class=\"fas fa-source-branch text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Video Source Type <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-field\"
                                                id=\"video_source_type\"
                                                name=\"video_source_type\"
                                                required
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                            <option value=\"upload\">Direct Upload</option>
                                            <option value=\"youtube\">YouTube Video</option>
                                            <option value=\"vdocipher\">VdoCipher (DRM Protected)</option>
                                        </select>
                                        <small class=\"form-text text-muted\">Choose how you want to provide the video content</small>
                                        <div class=\"invalid-feedback\">
                                            Please select a video source type.
                                        </div>
                                    </div>
                                </div>

                                <!-- Access Level -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"access_level\" class=\"form-label\">
                                            <i class=\"fas fa-shield-alt text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Access Level <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-field\"
                                                id=\"access_level\"
                                                name=\"access_level\"
                                                required
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                            <option value=\"public_free\">Public Free (No login required)</option>
                                            <option value=\"login_required_free\">Login Required Free (Registered users only)</option>
                                            <option value=\"premium\">Premium (Paid access)</option>
                                        </select>
                                        <small class=\"form-text text-muted\">Choose who can access this video</small>
                                        <div class=\"invalid-feedback\">
                                            Please select an access level.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Video Source Fields (Dynamic) -->
                            <!-- YouTube URL Field -->
                            <div class=\"form-group video-source-field\" data-source=\"youtube\" style=\"display: none;\">
                                <label for=\"youtube_url\" class=\"form-label\">
                                    <i class=\"fab fa-youtube text-danger mr-1\" aria-hidden=\"true\"></i>
                                    YouTube URL
                                </label>
                                <input type=\"url\"
                                       class=\"form-control enhanced-field\"
                                       id=\"youtube_url\"
                                       name=\"youtube_url\"
                                       placeholder=\"https://www.youtube.com/watch?v=...\"
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <small class=\"form-text text-muted\">Enter the full YouTube video URL</small>
                                <div class=\"invalid-feedback\">
                                    Please provide a valid YouTube URL.
                                </div>
                            </div>

                            <!-- VdoCipher Video ID Field -->
                            <div class=\"form-group video-source-field\" data-source=\"vdocipher\" style=\"display: none;\">
                                <label for=\"vdocipher_video_id\" class=\"form-label\">
                                    <i class=\"fas fa-shield-alt text-success mr-1\" aria-hidden=\"true\"></i>
                                    VdoCipher Video ID
                                </label>
                                <input type=\"text\"
                                       class=\"form-control enhanced-field\"
                                       id=\"vdocipher_video_id\"
                                       name=\"vdocipher_video_id\"
                                       placeholder=\"Enter VdoCipher video ID\"
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <small class=\"form-text text-muted\">Enter the VdoCipher video ID for DRM-protected streaming</small>
                                <div class=\"invalid-feedback\">
                                    Please provide a valid VdoCipher video ID.
                                </div>
                            </div>

                            <!-- Premium Video Settings -->
                            <div class=\"premium-settings\" style=\"display: none;\">
                                <div class=\"row\">
                                    <!-- Price -->
                                    <div class=\"col-md-6\">
                                        <div class=\"form-group\">
                                            <label for=\"price\" class=\"form-label\">
                                                <i class=\"fas fa-dollar-sign text-success mr-1\" aria-hidden=\"true\"></i>
                                                Price (USD)
                                            </label>
                                            <input type=\"number\"
                                                   class=\"form-control enhanced-field\"
                                                   id=\"price\"
                                                   name=\"price\"
                                                   placeholder=\"0.00\"
                                                   min=\"0\"
                                                   step=\"0.01\"
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                            <small class=\"form-text text-muted\">Set price for premium videos</small>
                                            <div class=\"invalid-feedback\">
                                                Please provide a valid price.
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Access Duration -->
                                    <div class=\"col-md-6\">
                                        <div class=\"form-group\">
                                            <label for=\"access_duration\" class=\"form-label\">
                                                <i class=\"fas fa-clock text-warning mr-1\" aria-hidden=\"true\"></i>
                                                Access Duration (Days)
                                            </label>
                                            <input type=\"number\"
                                                   class=\"form-control enhanced-field\"
                                                   id=\"access_duration\"
                                                   name=\"access_duration\"
                                                   placeholder=\"Leave empty for lifetime access\"
                                                   min=\"1\"
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                            <small class=\"form-text text-muted\">Number of days user will have access after purchase (leave empty for lifetime access)</small>
                                            <div class=\"invalid-feedback\">
                                                Please provide a valid access duration.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Video Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\" aria-hidden=\"true\"></i>
                                    Video Description <span class=\"text-danger\" aria-label=\"required\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"4\"
                                          placeholder=\"Enter video description\"
                                          required
                                          maxlength=\"2000\"
                                          style=\"border: 2px solid #ced4da;\"></textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide a video description.
                                </div>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    Provide a comprehensive description of the video content.
                                </small>
                            </div>

                            <!-- Video File Upload (Only for Direct Upload) -->
                            <div class=\"form-group video-source-field\" data-source=\"upload\">
                                <label for=\"video_file\" class=\"form-label\">
                                    <i class=\"fas fa-file-video text-primary mr-1\" aria-hidden=\"true\"></i>
                                    Video File <span class=\"text-danger\" aria-label=\"required\">*</span>
                                </label>
                                <input type=\"file\"
                                       class=\"form-control enhanced-file-field\"
                                       id=\"video_file\"
                                       name=\"video_file\"
                                       accept=\"video/*\"
                                       required
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    Upload a video file (MP4, AVI, MOV, WMV, WebM). Max size: 500MB
                                </small>

                                <!-- Video Preview -->
                                <div class=\"video-preview mt-3 d-flex flex-column align-items-center\" id=\"video-preview\" style=\"display: none;\">
                                    <div class=\"professional-video-container\" style=\"width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;\">
                                        <video controls class=\"w-100 h-100\" style=\"object-fit: cover;\" id=\"video-preview-player\">
                                            Your browser does not support the video tag.
                                        </video>
                                    </div>
                                    <small class=\"form-text text-success d-block mt-2 text-center\">Video Preview (600x300px)</small>
                                    <div id=\"video-file-info\" class=\"mt-2 text-center\"></div>
                                </div>

                                <div class=\"invalid-feedback\">
                                    Please select a video file.
                                </div>
                            </div>

                            <!-- Thumbnail Upload -->
                            <div class=\"form-group\">
                                <label for=\"thumbnail_file\" class=\"form-label\">
                                    <i class=\"fas fa-image text-primary mr-1\" aria-hidden=\"true\"></i>
                                    Video Thumbnail <span class=\"text-danger\" aria-label=\"required\">*</span>
                                </label>
                                <input type=\"file\"
                                       class=\"form-control enhanced-file-field\"
                                       id=\"thumbnail_file\"
                                       name=\"thumbnail_file\"
                                       accept=\"image/*\"
                                       required
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    Upload a thumbnail image (JPEG, PNG, WebP). Recommended size: 300x200px
                                </small>

                                <div class=\"image-preview mt-3 d-flex flex-column align-items-center\" id=\"thumbnail-preview\" style=\"display: none;\">
                                    <div class=\"professional-image-container\" style=\"width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;\">
                                        <img src=\"\" alt=\"Thumbnail Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\" id=\"thumbnail-preview-img\">
                                    </div>
                                    <small class=\"form-text text-success d-block mt-2 text-center\">Thumbnail Preview (300x200px)</small>
                                </div>

                                <div class=\"invalid-feedback\">
                                    Please select a thumbnail image.
                                </div>
                            </div>

                            <!-- Pricing Options Row -->
                            <div class=\"row\">
                                <!-- Pricing Type -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">
                                            <i class=\"fas fa-dollar-sign text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Pricing Option <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <div class=\"form-check\">
                                            <input class=\"form-check-input\" type=\"radio\" name=\"pricing_type\" id=\"pricing_free\" value=\"free\" checked>
                                            <label class=\"form-check-label\" for=\"pricing_free\">
                                                <i class=\"fas fa-gift text-success mr-1\"></i>
                                                Free Video
                                            </label>
                                        </div>
                                        <div class=\"form-check\">
                                            <input class=\"form-check-input\" type=\"radio\" name=\"pricing_type\" id=\"pricing_premium\" value=\"premium\">
                                            <label class=\"form-check-label\" for=\"pricing_premium\">
                                                <i class=\"fas fa-crown text-warning mr-1\"></i>
                                                Premium Video
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Price Input -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\" id=\"price-input-group\" style=\"display: none;\">
                                        <label for=\"price\" class=\"form-label\">
                                            <i class=\"fas fa-tag text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Price (USD) <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"price\"
                                               name=\"price\"
                                               placeholder=\"\"
                                               min=\"0\"
                                               step=\"0.01\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please enter a valid price.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Enter the price for premium access to this video.
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Hidden Fields -->
                            <input type=\"hidden\" id=\"is_free\" name=\"is_free\" value=\"1\">
                            <input type=\"hidden\" name=\"is_active\" value=\"1\">



                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 1px solid #dee2e6; padding: 1.5rem;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem; transition: all 0.3s ease;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Create Video
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"";
        // line 402
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_index");
        yield "\" class=\"btn btn-secondary btn-lg\" style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem;\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 414
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 415
        yield "<script>
\$(document).ready(function() {
    // Dynamic form field handling
    function toggleVideoSourceFields() {
        const sourceType = \$('#video_source_type').val();

        // Hide all source-specific fields
        \$('.video-source-field').hide();

        // Show relevant field based on source type
        \$(`.video-source-field[data-source=\"\${sourceType}\"]`).show();

        // Update required attributes
        \$('.video-source-field input').removeAttr('required');
        \$(`.video-source-field[data-source=\"\${sourceType}\"] input`).attr('required', 'required');
    }

    function toggleAccessLevelFields() {
        const accessLevel = \$('#access_level').val();

        if (accessLevel === 'premium') {
            \$('.premium-settings').show();
            \$('#price').attr('required', 'required');
        } else {
            \$('.premium-settings').hide();
            \$('#price').removeAttr('required');
            \$('#access_duration').removeAttr('required');
        }
    }

    // Initialize form state
    toggleVideoSourceFields();
    toggleAccessLevelFields();

    // Handle video source type changes
    \$('#video_source_type').on('change', function() {
        toggleVideoSourceFields();
    });

    // Handle access level changes
    \$('#access_level').on('change', function() {
        toggleAccessLevelFields();
    });

    // YouTube URL validation
    \$('#youtube_url').on('blur', function() {
        const url = \$(this).val();
        if (url && !isValidYouTubeUrl(url)) {
            \$(this).addClass('is-invalid');
            \$(this).siblings('.invalid-feedback').text('Please enter a valid YouTube URL');
        } else {
            \$(this).removeClass('is-invalid');
        }
    });

    function isValidYouTubeUrl(url) {
        const patterns = [
            /^https?:\\/\\/(www\\.)?(youtube\\.com\\/watch\\?v=|youtu\\.be\\/|youtube\\.com\\/embed\\/)([a-zA-Z0-9_-]{11})/,
            /^https?:\\/\\/(www\\.)?youtube\\.com\\/v\\/([a-zA-Z0-9_-]{11})/
        ];
        return patterns.some(pattern => pattern.test(url));
    }

    // Price formatting
    \$('#price').on('blur', function() {
        const value = parseFloat(\$(this).val());
        if (!isNaN(value)) {
            \$(this).val(value.toFixed(2));
        }
    });

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Create Video';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Creating Video...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Pricing radio button logic
    const pricingRadios = document.querySelectorAll('input[name=\"pricing_type\"]');
    const priceInputGroup = document.getElementById('price-input-group');
    const priceInput = document.getElementById('price');
    const isFreeInput = document.getElementById('is_free');

    function updatePricingDisplay() {
        const selectedValue = document.querySelector('input[name=\"pricing_type\"]:checked').value;
        if (selectedValue === 'free') {
            priceInputGroup.style.display = 'none';
            priceInput.value = '';
            isFreeInput.value = '1';
            priceInput.removeAttribute('required');
        } else if (selectedValue === 'premium') {
            priceInputGroup.style.display = 'block';
            priceInput.focus();
            isFreeInput.value = '0';
            priceInput.setAttribute('required', 'required');
        }
    }

    // Add event listeners to radio buttons
    pricingRadios.forEach(radio => {
        radio.addEventListener('change', updatePricingDisplay);
    });

    // Initialize state
    updatePricingDisplay();

    // Video file preview functionality
    const videoFileInput = document.getElementById('video_file');
    if (videoFileInput) {
        videoFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const previewContainer = document.getElementById('video-preview');
            const previewPlayer = document.getElementById('video-preview-player');
            const fileInfoDiv = document.getElementById('video-file-info');

            if (file && file.type.startsWith('video/')) {
                const url = URL.createObjectURL(file);
                previewPlayer.src = url;
                previewContainer.style.display = 'block';

                // Display file information
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                fileInfoDiv.innerHTML = `
                    <small class=\"form-text text-info\">
                        <i class=\"fas fa-info-circle mr-1\"></i>
                        <strong>File:</strong> \${file.name} |
                        <strong>Size:</strong> \${fileSize} MB |
                        <strong>Type:</strong> \${file.type}
                    </small>
                `;
            } else {
                previewContainer.style.display = 'none';
                fileInfoDiv.innerHTML = '';
            }
        });
    }

    // Thumbnail preview functionality
    const thumbnailFileInput = document.getElementById('thumbnail_file');
    if (thumbnailFileInput) {
        thumbnailFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const previewContainer = document.getElementById('thumbnail-preview');
            const previewImg = document.getElementById('thumbnail-preview-img');

            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    previewContainer.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                previewContainer.style.display = 'none';
            }
        });
    }

    // Enhanced form field focus effects with performance optimization
    \$('.form-control, .enhanced-field').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
        // Add subtle scale animation with GPU acceleration
        \$(this).css({
            'transform': 'scale(1.02)',
            'transition': 'transform 0.2s ease-out'
        });
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
        \$(this).css({
            'transform': 'scale(1)',
            'transition': 'transform 0.2s ease-out'
        });
    });

    // Performance-optimized file upload progress with visual feedback
    function setupAdvancedFileUpload() {
        const fileInputs = document.querySelectorAll('input[type=\"file\"]');

        fileInputs.forEach(input => {
            input.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // Create enhanced progress indicator
                    const progressContainer = document.createElement('div');
                    progressContainer.className = 'upload-progress-enhanced mt-3';
                    progressContainer.innerHTML = `
                        <div class=\"d-flex align-items-center mb-2\">
                            <div class=\"file-icon mr-3\">
                                <i class=\"fas fa-\${file.type.startsWith('video/') ? 'video' : 'image'} fa-2x text-primary\"></i>
                            </div>
                            <div class=\"flex-grow-1\">
                                <div class=\"font-weight-bold\">\${file.name}</div>
                                <small class=\"text-muted\">\${(file.size / 1024 / 1024).toFixed(2)} MB • \${file.type}</small>
                            </div>
                            <div class=\"upload-status\">
                                <i class=\"fas fa-clock text-warning\"></i>
                            </div>
                        </div>
                        <div class=\"progress\" style=\"height: 6px; border-radius: 3px; background-color: #e9ecef;\">
                            <div class=\"progress-bar bg-gradient-primary\" role=\"progressbar\" style=\"width: 0%; transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\"></div>
                        </div>
                        <div class=\"upload-details mt-1\" style=\"font-size: 0.875rem; color: #6c757d;\">
                            <span class=\"upload-speed\">Preparing...</span>
                        </div>
                    `;

                    // Remove existing progress indicators
                    const existingProgress = input.parentNode.querySelector('.upload-progress-enhanced');
                    if (existingProgress) {
                        existingProgress.remove();
                    }

                    input.parentNode.appendChild(progressContainer);

                    // Simulate realistic upload progress
                    const progressBar = progressContainer.querySelector('.progress-bar');
                    const uploadSpeed = progressContainer.querySelector('.upload-speed');
                    const uploadStatus = progressContainer.querySelector('.upload-status');

                    let progress = 0;
                    let speed = Math.random() * 5 + 2; // 2-7 MB/s
                    const totalSize = file.size / 1024 / 1024; // MB

                    const interval = setInterval(() => {
                        const increment = (speed / totalSize) * 100 * 0.1; // Simulate 100ms chunks
                        progress += increment + Math.random() * 2;

                        if (progress >= 100) {
                            progress = 100;
                            clearInterval(interval);

                            // Success state
                            uploadStatus.innerHTML = '';
                            uploadSpeed.textContent = 'Upload complete';
                            progressBar.classList.remove('bg-gradient-primary');
                            progressBar.classList.add('bg-success');

                            setTimeout(() => {
                                progressContainer.classList.add('fade-out');
                                setTimeout(() => {
                                    progressContainer.style.display = 'none';
                                }, 300);
                            }, 1000);
                        } else {
                            const currentSpeed = speed + (Math.random() - 0.5) * 2;
                            uploadSpeed.textContent = `\${currentSpeed.toFixed(1)} MB/s • \${(100 - progress).toFixed(0)}% remaining`;
                        }

                        progressBar.style.width = Math.min(progress, 100) + '%';
                    }, 100);
                }
            });
        });
    }

    // Initialize advanced file upload
    setupAdvancedFileUpload();

    // Enhanced real-time validation with smooth animations
    function setupSmoothValidation() {
        const requiredFields = document.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            let validationTimeout;

            field.addEventListener('input', function() {
                clearTimeout(validationTimeout);

                // Debounce validation for better performance
                validationTimeout = setTimeout(() => {
                    const isValid = this.checkValidity();
                    const formGroup = this.closest('.form-group');
                    const feedback = formGroup.querySelector('.invalid-feedback');

                    if (this.value.trim() !== '') {
                        if (isValid) {
                            this.classList.remove('is-invalid');
                            if (feedback) {
                                feedback.style.opacity = '0';
                                setTimeout(() => feedback.style.display = 'none', 200);
                            }
                        } else {
                            this.classList.add('is-invalid');
                            if (feedback) {
                                feedback.style.display = 'block';
                                feedback.style.opacity = '1';
                            }
                        }
                    } else {
                        this.classList.remove('is-valid', 'is-invalid');
                        if (feedback) {
                            feedback.style.opacity = '0';
                            setTimeout(() => feedback.style.display = 'none', 200);
                        }

                        // Remove success icon
                        const successIcon = formGroup.querySelector('.success-icon');
                        if (successIcon) {
                            successIcon.style.transform = 'translateY(-50%) scale(0)';
                            setTimeout(() => successIcon.remove(), 200);
                        }
                    }
                }, 300);
            });
        });
    }

    // Initialize smooth validation
    setupSmoothValidation();

    // Enhanced category selection with search functionality
    const categorySelect = document.getElementById('category');
    if (categorySelect) {
        \$(categorySelect).select2({
            placeholder: 'Search and select a category...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }
});
</script>

<!-- Include Select2 for enhanced dropdowns -->
<link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
<link href=\"https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css\" rel=\"stylesheet\" />
<script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

<style>
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control,
.form-group.focused .enhanced-field {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.enhanced-field:hover {
    border-color: #2a5298 !important;
}

.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px);
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Enhanced form controls styling */
.form-control,
.enhanced-field {
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus,
.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px) !important;
}

.form-control:hover,
.enhanced-field:hover {
    border-color: #2a5298 !important;
}

/* Fix dropdown display issues */
select.form-control,
select.enhanced-field {
    background-position: right 0.75rem center !important;
    background-size: 16px 12px !important;
    background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\") !important;
    background-repeat: no-repeat !important;
}

select.form-control option,
select.enhanced-field option {
    padding: 8px 12px !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    color: #212529 !important;
    background-color: #fff !important;
}

.professional-video-container,
.professional-image-container {
    transition: all 0.3s ease;
}

.professional-video-container:hover,
.professional-image-container:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
}

/* Enhanced Upload Progress Styling */
.upload-progress-enhanced {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.upload-progress-enhanced:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.file-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    flex-shrink: 0;
}

.bg-gradient-primary {
    background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%) !important;
}

.progress-bar {
    background-size: 20px 20px;
    background-image: linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent);
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 20px 0; }
    100% { background-position: 0 0; }
}

/* Fade out animation */
.fade-out {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

/* Enhanced Success Icon Animation */
.success-icon i {
    font-size: 1.2rem;
    filter: drop-shadow(0 2px 4px rgba(40, 167, 69, 0.3));
}

/* Performance optimizations */
.form-group {
    will-change: transform;
}

.progress-bar {
    will-change: width;
}

.success-icon {
    will-change: transform;
}

/* Select2 Custom Styling */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 12px !important;
    padding-right: 12px !important;
    line-height: calc(1.6em + 1.25rem) !important;
}

.select2-container--bootstrap4 .select2-selection--single:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.select2-container--bootstrap4 .select2-dropdown {
    border: 2px solid #1e3c72 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.15) !important;
}

.select2-container--bootstrap4 .select2-results__option--highlighted {
    background-color: #1e3c72 !important;
    color: white !important;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .form-control,
    .enhanced-field,
    .progress-bar,
    .success-icon,
    .upload-progress-enhanced {
        transition: none !important;
        animation: none !important;
        transform: none !important;
    }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/video/create.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  629 => 415,  616 => 414,  594 => 402,  293 => 103,  282 => 101,  278 => 100,  232 => 57,  215 => 43,  199 => 29,  189 => 25,  186 => 24,  182 => 23,  179 => 22,  169 => 18,  166 => 17,  162 => 16,  158 => 14,  145 => 13,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Create Video - Capitol Academy Admin{% endblock %}

{% block page_title %}Create New Video{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_video_index') }}\">Videos</a></li>
<li class=\"breadcrumb-item active\">Create Video</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-video mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Video
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Videos Button -->
                        <a href=\"{{ path('admin_video_index') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Videos
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('video_create') }}\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Video Title and Category Row -->
                            <div class=\"row\">
                                <!-- Video Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-video text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Video Title <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               placeholder=\"Enter video title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a video title.
                                        </div>
                                    </div>
                                </div>

                                <!-- Category -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"category\" class=\"form-label\">
                                            <i class=\"fas fa-folder text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Category <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"category\"
                                                name=\"category\"
                                                required
                                                aria-describedby=\"category_help category_error\"
                                                aria-label=\"Select a video category\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                            <option value=\"\">Choose a category...</option>
                                            {% for category in categories %}
                                                <option value=\"{{ category.name }}\">{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                        <div id=\"category_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                            Select the category that best describes this video. Use arrow keys to navigate options.
                                        </div>
                                        <div id=\"category_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Video Source Type and Access Level Row -->
                            <div class=\"row\">
                                <!-- Video Source Type -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"video_source_type\" class=\"form-label\">
                                            <i class=\"fas fa-source-branch text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Video Source Type <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-field\"
                                                id=\"video_source_type\"
                                                name=\"video_source_type\"
                                                required
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                            <option value=\"upload\">Direct Upload</option>
                                            <option value=\"youtube\">YouTube Video</option>
                                            <option value=\"vdocipher\">VdoCipher (DRM Protected)</option>
                                        </select>
                                        <small class=\"form-text text-muted\">Choose how you want to provide the video content</small>
                                        <div class=\"invalid-feedback\">
                                            Please select a video source type.
                                        </div>
                                    </div>
                                </div>

                                <!-- Access Level -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"access_level\" class=\"form-label\">
                                            <i class=\"fas fa-shield-alt text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Access Level <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-field\"
                                                id=\"access_level\"
                                                name=\"access_level\"
                                                required
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                            <option value=\"public_free\">Public Free (No login required)</option>
                                            <option value=\"login_required_free\">Login Required Free (Registered users only)</option>
                                            <option value=\"premium\">Premium (Paid access)</option>
                                        </select>
                                        <small class=\"form-text text-muted\">Choose who can access this video</small>
                                        <div class=\"invalid-feedback\">
                                            Please select an access level.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Video Source Fields (Dynamic) -->
                            <!-- YouTube URL Field -->
                            <div class=\"form-group video-source-field\" data-source=\"youtube\" style=\"display: none;\">
                                <label for=\"youtube_url\" class=\"form-label\">
                                    <i class=\"fab fa-youtube text-danger mr-1\" aria-hidden=\"true\"></i>
                                    YouTube URL
                                </label>
                                <input type=\"url\"
                                       class=\"form-control enhanced-field\"
                                       id=\"youtube_url\"
                                       name=\"youtube_url\"
                                       placeholder=\"https://www.youtube.com/watch?v=...\"
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <small class=\"form-text text-muted\">Enter the full YouTube video URL</small>
                                <div class=\"invalid-feedback\">
                                    Please provide a valid YouTube URL.
                                </div>
                            </div>

                            <!-- VdoCipher Video ID Field -->
                            <div class=\"form-group video-source-field\" data-source=\"vdocipher\" style=\"display: none;\">
                                <label for=\"vdocipher_video_id\" class=\"form-label\">
                                    <i class=\"fas fa-shield-alt text-success mr-1\" aria-hidden=\"true\"></i>
                                    VdoCipher Video ID
                                </label>
                                <input type=\"text\"
                                       class=\"form-control enhanced-field\"
                                       id=\"vdocipher_video_id\"
                                       name=\"vdocipher_video_id\"
                                       placeholder=\"Enter VdoCipher video ID\"
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <small class=\"form-text text-muted\">Enter the VdoCipher video ID for DRM-protected streaming</small>
                                <div class=\"invalid-feedback\">
                                    Please provide a valid VdoCipher video ID.
                                </div>
                            </div>

                            <!-- Premium Video Settings -->
                            <div class=\"premium-settings\" style=\"display: none;\">
                                <div class=\"row\">
                                    <!-- Price -->
                                    <div class=\"col-md-6\">
                                        <div class=\"form-group\">
                                            <label for=\"price\" class=\"form-label\">
                                                <i class=\"fas fa-dollar-sign text-success mr-1\" aria-hidden=\"true\"></i>
                                                Price (USD)
                                            </label>
                                            <input type=\"number\"
                                                   class=\"form-control enhanced-field\"
                                                   id=\"price\"
                                                   name=\"price\"
                                                   placeholder=\"0.00\"
                                                   min=\"0\"
                                                   step=\"0.01\"
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                            <small class=\"form-text text-muted\">Set price for premium videos</small>
                                            <div class=\"invalid-feedback\">
                                                Please provide a valid price.
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Access Duration -->
                                    <div class=\"col-md-6\">
                                        <div class=\"form-group\">
                                            <label for=\"access_duration\" class=\"form-label\">
                                                <i class=\"fas fa-clock text-warning mr-1\" aria-hidden=\"true\"></i>
                                                Access Duration (Days)
                                            </label>
                                            <input type=\"number\"
                                                   class=\"form-control enhanced-field\"
                                                   id=\"access_duration\"
                                                   name=\"access_duration\"
                                                   placeholder=\"Leave empty for lifetime access\"
                                                   min=\"1\"
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                            <small class=\"form-text text-muted\">Number of days user will have access after purchase (leave empty for lifetime access)</small>
                                            <div class=\"invalid-feedback\">
                                                Please provide a valid access duration.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Video Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\" aria-hidden=\"true\"></i>
                                    Video Description <span class=\"text-danger\" aria-label=\"required\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"4\"
                                          placeholder=\"Enter video description\"
                                          required
                                          maxlength=\"2000\"
                                          style=\"border: 2px solid #ced4da;\"></textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide a video description.
                                </div>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    Provide a comprehensive description of the video content.
                                </small>
                            </div>

                            <!-- Video File Upload (Only for Direct Upload) -->
                            <div class=\"form-group video-source-field\" data-source=\"upload\">
                                <label for=\"video_file\" class=\"form-label\">
                                    <i class=\"fas fa-file-video text-primary mr-1\" aria-hidden=\"true\"></i>
                                    Video File <span class=\"text-danger\" aria-label=\"required\">*</span>
                                </label>
                                <input type=\"file\"
                                       class=\"form-control enhanced-file-field\"
                                       id=\"video_file\"
                                       name=\"video_file\"
                                       accept=\"video/*\"
                                       required
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    Upload a video file (MP4, AVI, MOV, WMV, WebM). Max size: 500MB
                                </small>

                                <!-- Video Preview -->
                                <div class=\"video-preview mt-3 d-flex flex-column align-items-center\" id=\"video-preview\" style=\"display: none;\">
                                    <div class=\"professional-video-container\" style=\"width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;\">
                                        <video controls class=\"w-100 h-100\" style=\"object-fit: cover;\" id=\"video-preview-player\">
                                            Your browser does not support the video tag.
                                        </video>
                                    </div>
                                    <small class=\"form-text text-success d-block mt-2 text-center\">Video Preview (600x300px)</small>
                                    <div id=\"video-file-info\" class=\"mt-2 text-center\"></div>
                                </div>

                                <div class=\"invalid-feedback\">
                                    Please select a video file.
                                </div>
                            </div>

                            <!-- Thumbnail Upload -->
                            <div class=\"form-group\">
                                <label for=\"thumbnail_file\" class=\"form-label\">
                                    <i class=\"fas fa-image text-primary mr-1\" aria-hidden=\"true\"></i>
                                    Video Thumbnail <span class=\"text-danger\" aria-label=\"required\">*</span>
                                </label>
                                <input type=\"file\"
                                       class=\"form-control enhanced-file-field\"
                                       id=\"thumbnail_file\"
                                       name=\"thumbnail_file\"
                                       accept=\"image/*\"
                                       required
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    Upload a thumbnail image (JPEG, PNG, WebP). Recommended size: 300x200px
                                </small>

                                <div class=\"image-preview mt-3 d-flex flex-column align-items-center\" id=\"thumbnail-preview\" style=\"display: none;\">
                                    <div class=\"professional-image-container\" style=\"width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;\">
                                        <img src=\"\" alt=\"Thumbnail Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\" id=\"thumbnail-preview-img\">
                                    </div>
                                    <small class=\"form-text text-success d-block mt-2 text-center\">Thumbnail Preview (300x200px)</small>
                                </div>

                                <div class=\"invalid-feedback\">
                                    Please select a thumbnail image.
                                </div>
                            </div>

                            <!-- Pricing Options Row -->
                            <div class=\"row\">
                                <!-- Pricing Type -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label class=\"form-label\">
                                            <i class=\"fas fa-dollar-sign text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Pricing Option <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <div class=\"form-check\">
                                            <input class=\"form-check-input\" type=\"radio\" name=\"pricing_type\" id=\"pricing_free\" value=\"free\" checked>
                                            <label class=\"form-check-label\" for=\"pricing_free\">
                                                <i class=\"fas fa-gift text-success mr-1\"></i>
                                                Free Video
                                            </label>
                                        </div>
                                        <div class=\"form-check\">
                                            <input class=\"form-check-input\" type=\"radio\" name=\"pricing_type\" id=\"pricing_premium\" value=\"premium\">
                                            <label class=\"form-check-label\" for=\"pricing_premium\">
                                                <i class=\"fas fa-crown text-warning mr-1\"></i>
                                                Premium Video
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Price Input -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\" id=\"price-input-group\" style=\"display: none;\">
                                        <label for=\"price\" class=\"form-label\">
                                            <i class=\"fas fa-tag text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Price (USD) <span class=\"text-danger\" aria-label=\"required\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"price\"
                                               name=\"price\"
                                               placeholder=\"\"
                                               min=\"0\"
                                               step=\"0.01\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please enter a valid price.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            Enter the price for premium access to this video.
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Hidden Fields -->
                            <input type=\"hidden\" id=\"is_free\" name=\"is_free\" value=\"1\">
                            <input type=\"hidden\" name=\"is_active\" value=\"1\">



                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 1px solid #dee2e6; padding: 1.5rem;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem; transition: all 0.3s ease;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Create Video
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"{{ path('admin_video_index') }}\" class=\"btn btn-secondary btn-lg\" style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem;\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Dynamic form field handling
    function toggleVideoSourceFields() {
        const sourceType = \$('#video_source_type').val();

        // Hide all source-specific fields
        \$('.video-source-field').hide();

        // Show relevant field based on source type
        \$(`.video-source-field[data-source=\"\${sourceType}\"]`).show();

        // Update required attributes
        \$('.video-source-field input').removeAttr('required');
        \$(`.video-source-field[data-source=\"\${sourceType}\"] input`).attr('required', 'required');
    }

    function toggleAccessLevelFields() {
        const accessLevel = \$('#access_level').val();

        if (accessLevel === 'premium') {
            \$('.premium-settings').show();
            \$('#price').attr('required', 'required');
        } else {
            \$('.premium-settings').hide();
            \$('#price').removeAttr('required');
            \$('#access_duration').removeAttr('required');
        }
    }

    // Initialize form state
    toggleVideoSourceFields();
    toggleAccessLevelFields();

    // Handle video source type changes
    \$('#video_source_type').on('change', function() {
        toggleVideoSourceFields();
    });

    // Handle access level changes
    \$('#access_level').on('change', function() {
        toggleAccessLevelFields();
    });

    // YouTube URL validation
    \$('#youtube_url').on('blur', function() {
        const url = \$(this).val();
        if (url && !isValidYouTubeUrl(url)) {
            \$(this).addClass('is-invalid');
            \$(this).siblings('.invalid-feedback').text('Please enter a valid YouTube URL');
        } else {
            \$(this).removeClass('is-invalid');
        }
    });

    function isValidYouTubeUrl(url) {
        const patterns = [
            /^https?:\\/\\/(www\\.)?(youtube\\.com\\/watch\\?v=|youtu\\.be\\/|youtube\\.com\\/embed\\/)([a-zA-Z0-9_-]{11})/,
            /^https?:\\/\\/(www\\.)?youtube\\.com\\/v\\/([a-zA-Z0-9_-]{11})/
        ];
        return patterns.some(pattern => pattern.test(url));
    }

    // Price formatting
    \$('#price').on('blur', function() {
        const value = parseFloat(\$(this).val());
        if (!isNaN(value)) {
            \$(this).val(value.toFixed(2));
        }
    });

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Create Video';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Creating Video...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Pricing radio button logic
    const pricingRadios = document.querySelectorAll('input[name=\"pricing_type\"]');
    const priceInputGroup = document.getElementById('price-input-group');
    const priceInput = document.getElementById('price');
    const isFreeInput = document.getElementById('is_free');

    function updatePricingDisplay() {
        const selectedValue = document.querySelector('input[name=\"pricing_type\"]:checked').value;
        if (selectedValue === 'free') {
            priceInputGroup.style.display = 'none';
            priceInput.value = '';
            isFreeInput.value = '1';
            priceInput.removeAttribute('required');
        } else if (selectedValue === 'premium') {
            priceInputGroup.style.display = 'block';
            priceInput.focus();
            isFreeInput.value = '0';
            priceInput.setAttribute('required', 'required');
        }
    }

    // Add event listeners to radio buttons
    pricingRadios.forEach(radio => {
        radio.addEventListener('change', updatePricingDisplay);
    });

    // Initialize state
    updatePricingDisplay();

    // Video file preview functionality
    const videoFileInput = document.getElementById('video_file');
    if (videoFileInput) {
        videoFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const previewContainer = document.getElementById('video-preview');
            const previewPlayer = document.getElementById('video-preview-player');
            const fileInfoDiv = document.getElementById('video-file-info');

            if (file && file.type.startsWith('video/')) {
                const url = URL.createObjectURL(file);
                previewPlayer.src = url;
                previewContainer.style.display = 'block';

                // Display file information
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                fileInfoDiv.innerHTML = `
                    <small class=\"form-text text-info\">
                        <i class=\"fas fa-info-circle mr-1\"></i>
                        <strong>File:</strong> \${file.name} |
                        <strong>Size:</strong> \${fileSize} MB |
                        <strong>Type:</strong> \${file.type}
                    </small>
                `;
            } else {
                previewContainer.style.display = 'none';
                fileInfoDiv.innerHTML = '';
            }
        });
    }

    // Thumbnail preview functionality
    const thumbnailFileInput = document.getElementById('thumbnail_file');
    if (thumbnailFileInput) {
        thumbnailFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const previewContainer = document.getElementById('thumbnail-preview');
            const previewImg = document.getElementById('thumbnail-preview-img');

            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    previewContainer.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                previewContainer.style.display = 'none';
            }
        });
    }

    // Enhanced form field focus effects with performance optimization
    \$('.form-control, .enhanced-field').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
        // Add subtle scale animation with GPU acceleration
        \$(this).css({
            'transform': 'scale(1.02)',
            'transition': 'transform 0.2s ease-out'
        });
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
        \$(this).css({
            'transform': 'scale(1)',
            'transition': 'transform 0.2s ease-out'
        });
    });

    // Performance-optimized file upload progress with visual feedback
    function setupAdvancedFileUpload() {
        const fileInputs = document.querySelectorAll('input[type=\"file\"]');

        fileInputs.forEach(input => {
            input.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // Create enhanced progress indicator
                    const progressContainer = document.createElement('div');
                    progressContainer.className = 'upload-progress-enhanced mt-3';
                    progressContainer.innerHTML = `
                        <div class=\"d-flex align-items-center mb-2\">
                            <div class=\"file-icon mr-3\">
                                <i class=\"fas fa-\${file.type.startsWith('video/') ? 'video' : 'image'} fa-2x text-primary\"></i>
                            </div>
                            <div class=\"flex-grow-1\">
                                <div class=\"font-weight-bold\">\${file.name}</div>
                                <small class=\"text-muted\">\${(file.size / 1024 / 1024).toFixed(2)} MB • \${file.type}</small>
                            </div>
                            <div class=\"upload-status\">
                                <i class=\"fas fa-clock text-warning\"></i>
                            </div>
                        </div>
                        <div class=\"progress\" style=\"height: 6px; border-radius: 3px; background-color: #e9ecef;\">
                            <div class=\"progress-bar bg-gradient-primary\" role=\"progressbar\" style=\"width: 0%; transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\"></div>
                        </div>
                        <div class=\"upload-details mt-1\" style=\"font-size: 0.875rem; color: #6c757d;\">
                            <span class=\"upload-speed\">Preparing...</span>
                        </div>
                    `;

                    // Remove existing progress indicators
                    const existingProgress = input.parentNode.querySelector('.upload-progress-enhanced');
                    if (existingProgress) {
                        existingProgress.remove();
                    }

                    input.parentNode.appendChild(progressContainer);

                    // Simulate realistic upload progress
                    const progressBar = progressContainer.querySelector('.progress-bar');
                    const uploadSpeed = progressContainer.querySelector('.upload-speed');
                    const uploadStatus = progressContainer.querySelector('.upload-status');

                    let progress = 0;
                    let speed = Math.random() * 5 + 2; // 2-7 MB/s
                    const totalSize = file.size / 1024 / 1024; // MB

                    const interval = setInterval(() => {
                        const increment = (speed / totalSize) * 100 * 0.1; // Simulate 100ms chunks
                        progress += increment + Math.random() * 2;

                        if (progress >= 100) {
                            progress = 100;
                            clearInterval(interval);

                            // Success state
                            uploadStatus.innerHTML = '';
                            uploadSpeed.textContent = 'Upload complete';
                            progressBar.classList.remove('bg-gradient-primary');
                            progressBar.classList.add('bg-success');

                            setTimeout(() => {
                                progressContainer.classList.add('fade-out');
                                setTimeout(() => {
                                    progressContainer.style.display = 'none';
                                }, 300);
                            }, 1000);
                        } else {
                            const currentSpeed = speed + (Math.random() - 0.5) * 2;
                            uploadSpeed.textContent = `\${currentSpeed.toFixed(1)} MB/s • \${(100 - progress).toFixed(0)}% remaining`;
                        }

                        progressBar.style.width = Math.min(progress, 100) + '%';
                    }, 100);
                }
            });
        });
    }

    // Initialize advanced file upload
    setupAdvancedFileUpload();

    // Enhanced real-time validation with smooth animations
    function setupSmoothValidation() {
        const requiredFields = document.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            let validationTimeout;

            field.addEventListener('input', function() {
                clearTimeout(validationTimeout);

                // Debounce validation for better performance
                validationTimeout = setTimeout(() => {
                    const isValid = this.checkValidity();
                    const formGroup = this.closest('.form-group');
                    const feedback = formGroup.querySelector('.invalid-feedback');

                    if (this.value.trim() !== '') {
                        if (isValid) {
                            this.classList.remove('is-invalid');
                            if (feedback) {
                                feedback.style.opacity = '0';
                                setTimeout(() => feedback.style.display = 'none', 200);
                            }
                        } else {
                            this.classList.add('is-invalid');
                            if (feedback) {
                                feedback.style.display = 'block';
                                feedback.style.opacity = '1';
                            }
                        }
                    } else {
                        this.classList.remove('is-valid', 'is-invalid');
                        if (feedback) {
                            feedback.style.opacity = '0';
                            setTimeout(() => feedback.style.display = 'none', 200);
                        }

                        // Remove success icon
                        const successIcon = formGroup.querySelector('.success-icon');
                        if (successIcon) {
                            successIcon.style.transform = 'translateY(-50%) scale(0)';
                            setTimeout(() => successIcon.remove(), 200);
                        }
                    }
                }, 300);
            });
        });
    }

    // Initialize smooth validation
    setupSmoothValidation();

    // Enhanced category selection with search functionality
    const categorySelect = document.getElementById('category');
    if (categorySelect) {
        \$(categorySelect).select2({
            placeholder: 'Search and select a category...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }
});
</script>

<!-- Include Select2 for enhanced dropdowns -->
<link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
<link href=\"https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css\" rel=\"stylesheet\" />
<script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

<style>
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control,
.form-group.focused .enhanced-field {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.enhanced-field:hover {
    border-color: #2a5298 !important;
}

.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px);
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Enhanced form controls styling */
.form-control,
.enhanced-field {
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus,
.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px) !important;
}

.form-control:hover,
.enhanced-field:hover {
    border-color: #2a5298 !important;
}

/* Fix dropdown display issues */
select.form-control,
select.enhanced-field {
    background-position: right 0.75rem center !important;
    background-size: 16px 12px !important;
    background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\") !important;
    background-repeat: no-repeat !important;
}

select.form-control option,
select.enhanced-field option {
    padding: 8px 12px !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    color: #212529 !important;
    background-color: #fff !important;
}

.professional-video-container,
.professional-image-container {
    transition: all 0.3s ease;
}

.professional-video-container:hover,
.professional-image-container:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
}

/* Enhanced Upload Progress Styling */
.upload-progress-enhanced {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.upload-progress-enhanced:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.file-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    flex-shrink: 0;
}

.bg-gradient-primary {
    background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%) !important;
}

.progress-bar {
    background-size: 20px 20px;
    background-image: linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent);
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 20px 0; }
    100% { background-position: 0 0; }
}

/* Fade out animation */
.fade-out {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

/* Enhanced Success Icon Animation */
.success-icon i {
    font-size: 1.2rem;
    filter: drop-shadow(0 2px 4px rgba(40, 167, 69, 0.3));
}

/* Performance optimizations */
.form-group {
    will-change: transform;
}

.progress-bar {
    will-change: width;
}

.success-icon {
    will-change: transform;
}

/* Select2 Custom Styling */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 12px !important;
    padding-right: 12px !important;
    line-height: calc(1.6em + 1.25rem) !important;
}

.select2-container--bootstrap4 .select2-selection--single:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.select2-container--bootstrap4 .select2-dropdown {
    border: 2px solid #1e3c72 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.15) !important;
}

.select2-container--bootstrap4 .select2-results__option--highlighted {
    background-color: #1e3c72 !important;
    color: white !important;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .form-control,
    .enhanced-field,
    .progress-bar,
    .success-icon,
    .upload-progress-enhanced {
        transition: none !important;
        animation: none !important;
        transform: none !important;
    }
}
</style>
{% endblock %}
", "admin/video/create.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\video\\create.html.twig");
    }
}
