{% extends 'admin/base.html.twig' %}

{% block title %}Market Analysis Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Market Analysis Management{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Market Analysis</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Market Analysis Management',
    'page_icon': 'fas fa-chart-bar',
    'search_placeholder': 'Search articles by title, author, or asset type...',
    'create_button': {
        'url': path('admin_market_analysis_create'),
        'text': 'Add New Article',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Articles',
            'value': stats.total,
            'icon': 'fas fa-newspaper',
            'color': '#1e3c72',
            'gradient': 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
        },
        {
            'title': 'Active',
            'value': stats.published,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': stats.draft,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Thumbnail'},
            {'text': 'Title'},
            {'text': 'Asset Type'},
            {'text': 'Author'},
            {'text': 'Publish Date'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for article in articles %}
            {% set row_cells = [
                {
                    'content': '<div class="d-flex align-items-center">
                        <img src="' ~ article.thumbnailUrl ~ '" alt="Thumbnail" class="rounded" style="width: 50px; height: 40px; object-fit: cover;">
                    </div>'
                },
                {
                    'content': '<div class="article-title">
                        <strong>' ~ article.title ~ '</strong>
                        <br><small class="text-muted">' ~ article.shortExcerpt(60) ~ '</small>
                    </div>',
                    'class': 'article-title'
                },
                {
                    'content': '<span class="badge" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px;">' ~ article.assetTypeLabel ~ '</span>',
                    'class': 'article-asset-type'
                },
                {
                    'content': article.author ? '<span class="article-author">' ~ article.author ~ '</span>' : '<span class="text-muted">No author</span>',
                    'class': 'article-author'
                },
                {
                    'content': '<span class="text-muted">' ~ article.formattedPublishDate ~ '</span>'
                },
                {
                    'content': '<span class="badge ' ~ article.statusBadgeClass ~ ' px-3 py-2">' ~ article.statusLabel ~ '</span>'
                },

                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_market_analysis_show_readable', {'slug': article.slug}) ~ '" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="View Details"><i class="fas fa-eye"></i></a>
                        <a href="' ~ path('admin_market_analysis_edit_readable', {'slug': article.slug}) ~ '" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Edit Article"><i class="fas fa-edit"></i></a>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Toggle Status" onclick="toggleArticleStatus(' ~ article.id ~ ', \'' ~ article.title ~ '\', ' ~ article.isActive ~ ')"><i class="fas fa-toggle-on"></i></button>

                        <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Delete Article" onclick="deleteArticle(' ~ article.id ~ ', \'' ~ article.title ~ '\')"><i class="fas fa-trash"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'article-row',
            'empty_message': 'No market analysis articles found',
            'empty_icon': 'fas fa-chart-bar',
            'empty_description': 'Get started by adding your first market analysis article.',
            'search_config': {
                'fields': ['.article-title', '.article-author', '.article-asset-type']
            }
        } %}
    {% endblock %}
{% endembed %}





<!-- Include Modals -->
{% include 'admin/market_analysis/_modals.html.twig' %}
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.article-row',
        ['.article-title', '.article-author', '.article-asset-type']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// CSRF Token generation function
function generateCsrfToken(intention) {
    // For simplicity, we'll use a synchronous approach
    // In a real application, you might want to fetch this from the server
    const tokens = {
        {% for article in articles %}
        'toggle{{ article.id }}': '{{ csrf_token('toggle' ~ article.id) }}',

        'delete{{ article.id }}': '{{ csrf_token('delete' ~ article.id) }}',
        {% endfor %}
    };
    return tokens[intention] || '';
}

// Article management functions using standardized modals
function toggleArticleStatus(articleId, articleTitle, currentStatus) {
    showStatusModal(articleTitle, currentStatus, function() {
        executeStatusToggle(articleId);
    });
}

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}



function deleteArticle(articleId, articleTitle) {
    showDeleteModal(articleTitle, function() {
        executeArticleDelete(articleId);
    });
}

function executeStatusToggle(articleId) {
    // Generate CSRF token for this specific article
    fetch(`{{ path('admin_market_analysis_toggle_status', {'id': '__ID__'}) }}`.replace('__ID__', articleId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            '_token': generateCsrfToken('toggle' + articleId)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the success message - just reload the page
            setTimeout(() => location.reload(), 500);
        } else {
            showErrorToast(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorToast('An error occurred while updating the article status.');
    });
}



function executeArticleDelete(articleId) {
    fetch(`{{ path('admin_market_analysis_delete', {'id': '__ID__'}) }}`.replace('__ID__', articleId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            '_token': generateCsrfToken('delete' + articleId)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the success message - just reload the page
            setTimeout(() => location.reload(), 500);
        } else {
            showErrorToast(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorToast('An error occurred while deleting the article.');
    });
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Toast notification functions - REMOVED
/*
function showSuccessToast(message) {
    // Create a simple alert for now - can be replaced with a proper toast system
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
*/

function showErrorToast(message) {
    // Create a simple alert for now - can be replaced with a proper toast system
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
