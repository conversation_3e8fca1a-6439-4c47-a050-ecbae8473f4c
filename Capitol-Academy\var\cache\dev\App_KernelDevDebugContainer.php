<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerSGHcxSw\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerSGHcxSw/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerSGHcxSw.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerSGHcxSw\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerSGHcxSw\App_KernelDevDebugContainer([
    'container.build_hash' => 'SGHcxSw',
    'container.build_id' => '1871d51e',
    'container.build_time' => 1752770968,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerSGHcxSw');
