{% extends 'base.html.twig' %}

{% block title %}Video Purchase Successful - Capitol Academy{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .success-hero {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .success-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('/images/trading-bg-pattern.png') repeat;
            opacity: 0.05;
            z-index: 1;
        }

        .success-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 50px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
        }

        .success-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            animation: successPulse 2s infinite;
        }

        @keyframes successPulse {
            0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
            70% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
            100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
        }

        .success-title {
            color: #011a2d;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }

        .success-subtitle {
            color: #6c757d;
            font-size: 1.2rem;
            margin-bottom: 30px;
        }

        .video-info-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #28a745;
        }

        .video-title {
            color: #011a2d;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .access-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        .access-label {
            font-weight: 600;
            color: #495057;
        }

        .access-value {
            color: #28a745;
            font-weight: 600;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 40px;
        }

        .btn-watch {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .btn-watch:hover {
            background: linear-gradient(135deg, #8b0314, #a90418);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(169, 4, 24, 0.3);
            color: white;
            text-decoration: none;
        }

        .btn-dashboard {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .btn-dashboard:hover {
            background: linear-gradient(135deg, #1a3461, #011a2d);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(1, 26, 45, 0.3);
            color: white;
            text-decoration: none;
        }

        .payment-details {
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            text-align: left;
        }

        .payment-details h5 {
            color: #011a2d;
            margin-bottom: 15px;
        }

        .payment-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }

        .payment-row:last-child {
            border-bottom: none;
            font-weight: 600;
            color: #011a2d;
        }

        @media (max-width: 768px) {
            .success-card {
                padding: 30px 20px;
                margin: 20px;
            }
            
            .success-title {
                font-size: 2rem;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .access-info {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>
{% endblock %}

{% block body %}
<section class="success-hero">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="success-card">
                    <!-- Success Icon -->
                    <div class="success-icon">
                        <i class="fas fa-check text-white" style="font-size: 3rem;"></i>
                    </div>

                    <!-- Success Message -->
                    <h1 class="success-title">Payment Successful!</h1>
                    <p class="success-subtitle">
                        Your video purchase has been completed successfully. You now have access to watch this premium content.
                    </p>

                    <!-- Video Information -->
                    <div class="video-info-card">
                        <h3 class="video-title">{{ video.title }}</h3>
                        
                        <div class="access-info">
                            <span class="access-label">Access Level:</span>
                            <span class="access-value">
                                <i class="fas fa-crown"></i> Premium Access
                            </span>
                        </div>

                        <div class="access-info">
                            <span class="access-label">Access Duration:</span>
                            <span class="access-value">
                                {% if expires_at %}
                                    <i class="fas fa-clock"></i> {{ video.formattedAccessDuration }}
                                    <small class="d-block text-muted">Expires: {{ expires_at|date('M d, Y g:i A') }}</small>
                                {% else %}
                                    <i class="fas fa-infinity"></i> Lifetime Access
                                {% endif %}
                            </span>
                        </div>

                        <div class="access-info">
                            <span class="access-label">Video Source:</span>
                            <span class="access-value">
                                {% if video.videoSourceType == 'youtube' %}
                                    <i class="fab fa-youtube"></i> YouTube
                                {% elseif video.videoSourceType == 'vdocipher' %}
                                    <i class="fas fa-shield-alt"></i> VdoCipher (DRM Protected)
                                {% else %}
                                    <i class="fas fa-play"></i> Direct Stream
                                {% endif %}
                            </span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <a href="{{ path('app_video_show', {'id': video.id}) }}" class="btn-watch">
                            <i class="fas fa-play"></i>
                            Watch Now
                        </a>
                        <a href="{{ path('app_user_dashboard') }}" class="btn-dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            My Dashboard
                        </a>
                    </div>

                    <!-- Payment Details -->
                    <div class="payment-details">
                        <h5><i class="fas fa-receipt"></i> Payment Details</h5>
                        <div class="payment-row">
                            <span>Video:</span>
                            <span>{{ video.title }}</span>
                        </div>
                        <div class="payment-row">
                            <span>Amount:</span>
                            <span>{{ video.formattedPrice }}</span>
                        </div>
                        <div class="payment-row">
                            <span>Payment Method:</span>
                            <span>Credit Card</span>
                        </div>
                        <div class="payment-row">
                            <span>Transaction ID:</span>
                            <span>{{ session.id }}</span>
                        </div>
                        <div class="payment-row">
                            <span>Date:</span>
                            <span>{{ "now"|date('M d, Y g:i A') }}</span>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="mt-4">
                        <p class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            A confirmation email has been sent to your registered email address.
                            You can access this video anytime from your dashboard.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Auto-redirect to video after 10 seconds (optional)
        setTimeout(function() {
            const watchButton = document.querySelector('.btn-watch');
            if (watchButton) {
                watchButton.style.animation = 'pulse 1s infinite';
            }
        }, 5000);

        // Confetti effect (optional enhancement)
        document.addEventListener('DOMContentLoaded', function() {
            // Simple confetti effect could be added here
            console.log('Video purchase successful!');
        });
    </script>
{% endblock %}
