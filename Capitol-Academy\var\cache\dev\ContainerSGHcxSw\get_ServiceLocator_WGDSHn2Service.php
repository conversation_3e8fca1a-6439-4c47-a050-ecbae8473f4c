<?php

namespace ContainerSGHcxSw;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_WGDSHn2Service extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.WGDSHn2' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.WGDSHn2'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'article' => ['privates', '.errored..service_locator.WGDSHn2.App\\Entity\\MarketAnalysis', NULL, 'Cannot autowire service ".service_locator.WGDSHn2": it needs an instance of "App\\Entity\\MarketAnalysis" but this type has been excluded in "config/services.yaml".'],
        ], [
            'article' => 'App\\Entity\\MarketAnalysis',
        ]);
    }
}
