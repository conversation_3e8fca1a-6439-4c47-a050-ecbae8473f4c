<?php

namespace App\Controller;

use App\Entity\Payment;
use App\Entity\Video;
use App\Entity\User;
use App\Repository\CourseRepository;
use App\Repository\VideoRepository;
use App\Repository\PaymentRepository;
use App\Service\StripeService;
use App\Service\AdminNotificationService;
use App\Service\AccessControlService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Psr\Log\LoggerInterface;

class PaymentController extends AbstractController
{
    public function __construct(
        private StripeService $stripeService,
        private EntityManagerInterface $entityManager,
        private PaymentRepository $paymentRepository,
        private AdminNotificationService $adminNotificationService,
        private AccessControlService $accessControlService,
        private LoggerInterface $logger
    ) {}

    #[Route('/course/{code}/contact', name: 'course_contact_redirect', methods: ['POST'])]
    public function redirectToContact(
        string $code,
        CourseRepository $courseRepository
    ): JsonResponse {
        try {
            $course = $courseRepository->findOneBy(['code' => $code]);
            if (!$course) {
                return new JsonResponse(['error' => 'Course not found'], 404);
            }

            // Since we simplified the course system, redirect to contact form
            return new JsonResponse([
                'success' => true,
                'message' => 'Please contact us for course enrollment information',
                'redirect_url' => $this->generateUrl('public_course_detail', ['code' => $course->getCode()])
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Course contact redirect error: ' . $e->getMessage());
            return new JsonResponse(['error' => 'An error occurred'], 500);
        }
    }

    #[Route('/payment/success', name: 'payment_success')]
    public function paymentSuccess(Request $request, VideoRepository $videoRepository): Response
    {
        $sessionId = $request->query->get('session_id');

        if (!$sessionId) {
            $this->addFlash('error', 'Invalid payment session');
            return $this->redirectToRoute('app_home');
        }

        try {
            $session = $this->stripeService->retrieveCheckoutSession($sessionId);

            // Check if this is a video payment
            if (isset($session->metadata['product_type']) && $session->metadata['product_type'] === 'video') {
                return $this->handleVideoPaymentSuccess($session, $videoRepository);
            }

            // Handle course payment (existing logic)
            $payment = $this->paymentRepository->findByStripePaymentId($sessionId);

            if (!$payment) {
                $this->addFlash('error', 'Payment record not found');
                return $this->redirectToRoute('app_home');
            }

            // Update payment status if successful
            if ($session->payment_status === 'paid') {
                $payment->setStatus('succeeded');
                $payment->setStripeData(array_merge($payment->getStripeData() ?? [], [
                    'payment_intent' => $session->payment_intent,
                    'payment_status' => $session->payment_status,
                    'amount_total' => $session->amount_total
                ]));

                $this->entityManager->flush();

                // Notify admin about successful payment (without enrollment)
                $this->adminNotificationService->notifyPaymentSuccess($payment);

                return $this->render('payment/success.html.twig', [
                    'course' => $payment->getCourse(),
                    'payment' => $payment
                ]);
            }

        } catch (\Exception $e) {
            $this->logger->error('Payment success handling error: ' . $e->getMessage());
            $this->addFlash('error', 'Error processing payment confirmation');
        }
        
        return $this->redirectToRoute('app_home');
    }

    #[Route('/payment/cancel', name: 'payment_cancel')]
    public function paymentCancel(): Response
    {
        return $this->render('payment/cancel.html.twig');
    }

    private function handleVideoPaymentSuccess($session, VideoRepository $videoRepository): Response
    {
        $videoId = $session->metadata['video_id'] ?? null;
        $userId = $session->metadata['user_id'] ?? null;
        $accessDuration = $session->metadata['access_duration'] ?? null;

        if (!$videoId || !$userId) {
            $this->addFlash('error', 'Invalid video payment data');
            return $this->redirectToRoute('app_home');
        }

        $video = $videoRepository->find($videoId);
        $user = $this->entityManager->getRepository(User::class)->find($userId);

        if (!$video || !$user) {
            $this->addFlash('error', 'Video or user not found');
            return $this->redirectToRoute('app_home');
        }

        // Grant video access to user
        $expiresAt = null;
        if ($accessDuration && is_numeric($accessDuration)) {
            $expiresAt = (new \DateTimeImmutable())->modify("+{$accessDuration} days");
        }

        try {
            $this->accessControlService->grantVideoAccess(
                $user,
                $video,
                $expiresAt,
                'stripe_purchase',
                null, // orderId - we could create a video order entity if needed
                null  // videoPlanId
            );

            $this->addFlash('success', 'Video access granted successfully!');

            return $this->render('payment/video_success.html.twig', [
                'video' => $video,
                'user' => $user,
                'access_duration' => $accessDuration,
                'expires_at' => $expiresAt,
                'session' => $session
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Video access grant error: ' . $e->getMessage());
            $this->addFlash('error', 'Error granting video access. Please contact support.');
            return $this->redirectToRoute('app_home');
        }
    }

    #[Route('/stripe/webhook', name: 'stripe_webhook', methods: ['POST'])]
    public function stripeWebhook(Request $request): Response
    {
        $payload = $request->getContent();
        $signature = $request->headers->get('stripe-signature');
        
        if (!$signature) {
            $this->logger->warning('Stripe webhook: Missing signature');
            return new Response('Missing signature', 400);
        }

        try {
            $event = $this->stripeService->constructWebhookEvent($payload, $signature);
            
            $this->logger->info('Stripe webhook received: ' . $event->type);
            
            switch ($event->type) {
                case 'checkout.session.completed':
                    $this->handleCheckoutSessionCompleted($event->data->object);
                    break;
                    
                case 'payment_intent.succeeded':
                    $this->handlePaymentIntentSucceeded($event->data->object);
                    break;
                    
                case 'payment_intent.payment_failed':
                    $this->handlePaymentIntentFailed($event->data->object);
                    break;
                    
                default:
                    $this->logger->info('Unhandled webhook event type: ' . $event->type);
            }
            
            return new Response('Webhook handled', 200);
            
        } catch (\Exception $e) {
            $this->logger->error('Stripe webhook error: ' . $e->getMessage());
            return new Response('Webhook error', 400);
        }
    }

    private function handleCheckoutSessionCompleted($session): void
    {
        $payment = $this->paymentRepository->findByStripePaymentId($session->id);
        
        if ($payment && $session->payment_status === 'paid') {
            $payment->setStatus('succeeded');
            $payment->setStripeData(array_merge($payment->getStripeData() ?? [], [
                'payment_intent' => $session->payment_intent,
                'payment_status' => $session->payment_status,
                'amount_total' => $session->amount_total
            ]));
            
            $this->entityManager->flush();

            // Send admin notification about successful payment
            $this->adminNotificationService->notifyPaymentSuccess($payment);

            $this->logger->info('Payment processed successfully: ' . $payment->getId());
        }
    }

    private function handlePaymentIntentSucceeded($paymentIntent): void
    {
        // Additional handling if needed
        $this->logger->info('Payment intent succeeded: ' . $paymentIntent->id);
    }

    private function handlePaymentIntentFailed($paymentIntent): void
    {
        // Handle failed payments
        $this->logger->warning('Payment intent failed: ' . $paymentIntent->id);

        // Find payment by payment intent ID and notify admins
        $payment = $this->paymentRepository->findByStripePaymentIntentId($paymentIntent->id);
        if ($payment) {
            $payment->setStatus('failed');
            $this->entityManager->flush();

            $this->adminNotificationService->notifyPaymentFailure($payment);
        }
    }
}
