<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}Capitol Academy Admin{% endblock %}</title>

    <!-- Professional Favicon System -->
    <link rel="icon" type="image/png" href="{{ asset('images/logos/logo-round.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/logos/logo-round.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/logos/logo-round.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/logos/logo-round.png') }}">
    <link rel="icon" type="image/png" sizes="192x192" href="{{ asset('images/logos/logo-round.png') }}">
    <link rel="icon" type="image/png" sizes="512x512" href="{{ asset('images/logos/logo-round.png') }}">
    <meta name="theme-color" content="#1e3c72">

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- AdminLTE -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
    <!-- Capitol Academy Admin Styles -->
    <link rel="stylesheet" href="{{ asset('css/admin-styles.css') }}">

    <style>
        /* Capitol Academy Professional Styles */
        :root {
            --ca-primary-blue: #1e3c72;
            --ca-secondary-blue: #2a5298;
            --ca-accent-red: #dc3545;
            --ca-accent-red-dark: #c82333;
            --ca-dark-gray: #343a40;
            --ca-medium-gray: #6c757d;
            --ca-light-gray: #f8f9fa;
            --ca-border-gray: #dee2e6;
            --ca-white: #ffffff;
        }

        /* Professional Navigation Styles */

        /* Enhanced Topbar and Dropdown Effects */
        #adminDropdownToggle {
            transition: all 0.3s ease !important;
        }

        #adminDropdownToggle:hover {
            background: rgba(255,255,255,0.2) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(0,0,0,0.3) !important;
            border-color: rgba(255,255,255,0.4) !important;
        }

        .dropdown-menu {
            border: none !important;
            box-shadow: 0 12px 40px rgba(0,0,0,0.2) !important;
            border-radius: 12px !important;
            overflow: hidden !important;
            backdrop-filter: blur(20px) !important;
            z-index: 9999 !important;
        }

        .dropdown-item:hover {
            background: #f8f9fa !important;
            color: #1e3c72 !important;
        }

        /* Professional Minimalist Sidebar Navigation */
        .minimal-nav-link:hover {
            background: #f8f9fa !important;
            color: #1e3c72 !important;
        }

        .minimal-nav-link:hover i {
            color: #1e3c72 !important;
        }

        .minimal-nav-link.active {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
            color: white !important;
            box-shadow: 0 2px 8px rgba(30, 60, 114, 0.2) !important;
        }

        .minimal-nav-link.active i {
            color: white !important;
        }

        .minimal-nav-link.active span {
            color: white !important;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
            color: white !important;
            transform: translateX(4px) !important;
        }

        /* Professional Sidebar Navigation Effects */
        .minimal-nav-link:hover {
            background: linear-gradient(135deg, rgba(30, 60, 114, 0.1) 0%, rgba(42, 82, 152, 0.1) 100%) !important;
            border-color: #1e3c72 !important;
            transform: translateX(4px) !important;
            box-shadow: 0 4px 12px rgba(30, 60, 114, 0.15) !important;
        }

        .minimal-nav-link.active {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
            color: white !important;
            border-color: #dc3545 !important;
            box-shadow: 0 6px 20px rgba(30, 60, 114, 0.3) !important;
        }

        .minimal-nav-link.active i {
            color: white !important;
        }

        .minimal-nav-link.active span {
            color: white !important;
        }

        .dropdown-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border-bottom: 1px solid #e1e5e9 !important;
            font-weight: 600 !important;
            letter-spacing: 0.5px !important;
            text-transform: uppercase !important;
            font-size: 0.75rem !important;
            padding: 12px 16px !important;
        }

        .dropdown-item {
            transition: all 0.3s ease !important;
            padding: 12px 16px !important;
            border-left: 3px solid transparent !important;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, rgba(30, 60, 114, 0.08) 0%, rgba(30, 60, 114, 0.12) 100%) !important;
            color: var(--ca-primary-blue) !important;
            border-left: 3px solid var(--ca-primary-blue) !important;
            transform: translateX(2px) !important;
        }

        .dropdown-item.text-danger:hover {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.08) 0%, rgba(220, 53, 69, 0.12) 100%) !important;
            color: #dc3545 !important;
            border-left: 3px solid #dc3545 !important;
        }

        .dropdown-divider {
            margin: 8px 0 !important;
            border-top: 1px solid #e1e5e9 !important;
            position: relative !important;
        }

        .dropdown-divider::before {
            content: '';
            position: absolute;
            top: -1px;
            left: 16px;
            right: 16px;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(30, 60, 114, 0.1), transparent);
        }
        
        /* Professional Sidebar Navigation Styles */
        .minimal-nav-link {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease !important;
        }

        .minimal-nav-link:hover {
            background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%) !important;
            color: var(--ca-dark-gray) !important;
            text-decoration: none !important;
            transform: translateX(4px) !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08) !important;
            border-radius: 6px !important;
        }

        .minimal-nav-link:hover i {
            color: var(--ca-primary-blue) !important;
            transform: scale(1.1) !important;
        }

        .minimal-nav-link.active {
            background: linear-gradient(135deg, rgba(30, 60, 114, 0.08) 0%, rgba(30, 60, 114, 0.12) 100%) !important;
            color: var(--ca-primary-blue) !important;
            border-left: 4px solid var(--ca-primary-blue) !important;
            margin-left: -16px !important;
            padding-left: 28px !important;
            box-shadow: 0 3px 12px rgba(30, 60, 114, 0.15) !important;
            border-radius: 0 6px 6px 0 !important;
            font-weight: 600 !important;
        }

        .minimal-nav-link.active i {
            color: var(--ca-primary-blue) !important;
            transform: scale(1.15) !important;
        }

        .minimal-nav-link.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(30, 60, 114, 0.03), transparent);
            pointer-events: none;
        }
        
        /* Content wrapper adjustments */
        .content-wrapper {
            margin-left: 250px !important;
            margin-top: 60px !important;
            width: calc(100% - 250px) !important;
            min-height: calc(100vh - 60px) !important;
            background: var(--ca-light-gray) !important;
        }
        
        /* Footer adjustments */
        .main-footer {
            margin-left: 250px !important;
            background: var(--ca-white) !important;
            position: relative !important;
            margin-top: 3rem !important;
            padding-top: 2rem !important;
        }


        
        /* Enhanced Mobile responsive */
        @media (max-width: 768px) {
            .main-sidebar {
                transform: translateX(-250px);
                transition: transform 0.3s ease;
                z-index: 1040;
            }

            .main-sidebar.show {
                transform: translateX(0);
                box-shadow: 2px 0 20px rgba(0,0,0,0.3);
            }

            .content-wrapper {
                margin-left: 0 !important;
                width: 100% !important;
                margin-top: 60px !important;
            }

            .main-footer {
                margin-left: 0 !important;
            }



            .navbar-brand span {
                font-size: 0.9rem;
            }

            /* Mobile overlay */
            .mobile-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                z-index: 1035;
                display: none;
            }

            .mobile-overlay.show {
                display: block;
            }
        }

        @media (max-width: 576px) {
            .navbar-brand span {
                display: none;
            }

            .content-header h1 {
                font-size: 1.5rem;
            }

            .card {
                margin-bottom: 1rem;
            }

            .btn {
                font-size: 0.875rem;
                padding: 0.375rem 0.75rem;
            }
        }
        
        /* Clean layout */
        .wrapper {
            min-height: 100vh !important;
        }
        
        /* Clean card styling */
        .card {
            border: 1px solid var(--ca-border-gray);
            border-radius: 8px;
        }
        
        .card-header {
            background: var(--ca-light-gray);
            border-bottom: 1px solid var(--ca-border-gray);
        }
        
        /* Clean button styling */
        .btn-primary {
            background-color: var(--ca-primary-blue);
            border-color: var(--ca-primary-blue);
        }
        
        .btn-primary:hover {
            background-color: #1a3461;
            border-color: #1a3461;
        }
        
        /* Clean table styling */
        .table th {
            background-color: var(--ca-light-gray);
            border-top: none;
            color: var(--ca-dark-gray);
            font-weight: 600;
        }
        
        /* Clean badge styling */
        .badge-primary {
            background-color: var(--ca-primary-blue);
        }

        /* Enhanced Loading States */
        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .preloader.fade-out {
            opacity: 0;
            visibility: hidden;
        }

        .preloader-content {
            text-align: center;
            color: white;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 9998;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        /* Enhanced Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .slide-in-left {
            animation: slideInLeft 0.6s ease-out;
        }

        /* Enhanced Form Styling */
        .form-control:focus {
            border-color: var(--ca-primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
        }

        .form-select:focus {
            border-color: var(--ca-primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
        }

        /* Enhanced Button Styling */
        .btn {
            transition: all 0.3s ease;
            border-radius: 6px;
            font-weight: 500;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn:active {
            transform: translateY(0);
        }

        /* Enhanced Alert Styling */
        .alert {
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 0.5s ease-out;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }

        .alert-info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
        }

        /* Ripple Effect */
        .btn {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Enhanced Table Styling */
        .table {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .table thead th {
            background: linear-gradient(135deg, var(--ca-light-gray) 0%, #e9ecef 100%);
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
            padding: 1rem 0.75rem;
        }

        .table tbody tr {
            transition: all 0.3s ease;
            border: none;
        }

        .table tbody tr:hover {
            background-color: rgba(1, 26, 45, 0.05) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* Enhanced Admin Statistics Cards */
        .admin-stat-card {
            transition: all 0.3s ease;
            border-radius: 12px;
            overflow: hidden;
        }

        .admin-stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }

        .admin-stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .admin-stat-card:hover .admin-stat-icon {
            transform: scale(1.1);
        }

        /* Enhanced Button Styling */
        .btn-group .btn {
            transition: all 0.3s ease;
            margin-right: 2px;
        }

        .btn-group .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 10;
            position: relative;
        }

        /* Enhanced Search Input */
        .admin-search-input:focus {
            border-color: var(--ca-primary-blue) !important;
            box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
            transform: scale(1.02);
        }

        .admin-search-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
        }

        /* Enhanced Card Styling */
        .card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }

        .card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        /* Enhanced Badge Styling */
        .badge {
            transition: all 0.3s ease;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .badge:hover {
            transform: scale(1.05);
        }

        /* Enhanced Modal Styling */
        .modal-content {
            border: none;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
            border-bottom: 1px solid rgba(1, 26, 45, 0.1);
        }

        .modal-footer {
            border-top: 1px solid rgba(1, 26, 45, 0.1);
        }

        /* Enhanced Loading States */
        .loading-overlay {
            backdrop-filter: blur(5px);
        }

        /* Enhanced Animations */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 768px) {
            .admin-stat-card .card-body {
                padding: 1rem;
            }

            .admin-stat-icon {
                width: 40px;
                height: 40px;
            }

            .btn-group .btn {
                padding: 0.375rem 0.5rem;
                font-size: 0.875rem;
            }
        }

        /* Professional Focus States */
        .form-control:focus,
        .form-select:focus {
            border-color: var(--ca-primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25);
        }

        /* Enhanced Dropdown Styling */
        .dropdown-menu {
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border-radius: 8px;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, rgba(1, 26, 45, 0.08) 0%, rgba(1, 26, 45, 0.12) 100%);
            color: var(--ca-primary-blue);
        }
            background-color: rgba(30, 60, 114, 0.05) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table tbody td {
            border: none;
            border-bottom: 1px solid #f0f0f0;
            padding: 1rem 0.75rem;
            vertical-align: middle;
        }

        /* Enhanced Card Styling */
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            background: linear-gradient(135deg, var(--ca-light-gray) 0%, #f8f9fa 100%);
            border: none;
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            color: var(--ca-dark-gray);
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Enhanced Badge Styling */
        .badge {
            border-radius: 6px;
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Enhanced Input Styling */
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--ca-primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.15);
            transform: translateY(-1px);
        }

        /* Enhanced Search Box */
        .search-box {
            position: relative;
        }

        .search-box input {
            padding-left: 2.5rem;
        }

        .search-box::before {
            content: '\f002';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 10;
        }
    </style>

    {% block stylesheets %}{% endblock %}
</head>
<body class="hold-transition sidebar-mini layout-fixed layout-navbar-fixed">
<div class="wrapper">

    <!-- Loading Spinner -->
    {% include 'components/loading_spinner.html.twig' %}

    <!-- Loading Overlay for AJAX requests -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner-border text-light" role="status">
                <span class="visually-hidden">Processing...</span>
            </div>
            <p class="mt-2 text-light">Processing request...</p>
        </div>
    </div>

    <!-- Professional Minimalist Topbar -->
    <nav class="main-header navbar navbar-expand navbar-light" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); border-bottom: 2px solid #dc3545; height: 60px; position: fixed; top: 0; left: 0; right: 0; width: 100%; z-index: 1030; box-shadow: 0 2px 15px rgba(30, 60, 114, 0.2); margin: 0; padding: 0;">
        <div class="container-fluid" style="padding: 0; margin: 0; width: 100%;">
            <!-- Left: Capitol Academy Branding -->
            <div class="navbar-brand d-flex align-items-center" style="margin: 0; padding-left: 20px;">
                <img src="{{ asset('images/logos/logo-round.png') }}" alt="Capitol Academy" class="logo-circle" style="width: 32px; height: 32px; margin-right: 12px; filter: brightness(0) invert(1);" onerror="this.style.display='none'">
                <span style="font-weight: 600; font-size: 1.1rem; color: white;">Capitol Academy Admin</span>
            </div>

            <!-- Right: Admin Profile Dropdown -->
            <ul class="navbar-nav ml-auto" style="margin-right: 20px;">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center"
                       data-toggle="dropdown"
                       href="#"
                       role="button"
                       aria-haspopup="true"
                       aria-expanded="false"
                       id="adminDropdownToggle"
                       style="color: white; padding: 8px 16px; border-radius: 6px; transition: all 0.3s ease; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2);">
                        <img src="{{ admin_image_url(app.user ? app.user.profileImageFilename : null) }}"
                             alt="Admin Profile"
                             style="width: 28px; height: 28px; border-radius: 50%; object-fit: cover; margin-right: 8px; border: 2px solid rgba(255,255,255,0.3);"
                             onerror="this.src='{{ admin_image_url() }}'">
                        <span style="font-weight: 500; font-size: 0.9rem;">{{ app.user ? app.user.username : 'Administrator' }}</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow-lg"
                         aria-labelledby="adminDropdownToggle"
                         style="min-width: 200px; margin-top: 5px; border: none; border-radius: 8px; background: white; z-index: 9999; box-shadow: 0 4px 20px rgba(0,0,0,0.15);">
                        <a class="dropdown-item d-flex align-items-center" href="{{ path('admin_profile') }}" style="padding: 12px 16px; transition: all 0.3s ease;">
                            <i class="fas fa-user-circle mr-3" style="color: #1e3c72; font-size: 1rem;"></i>
                            <span style="font-weight: 500; color: #343a40;">Admin Profile</span>
                        </a>
                        <div class="dropdown-divider" style="margin: 0.25rem 0;"></div>
                        <a class="dropdown-item d-flex align-items-center" href="{{ path('admin_logout') }}" style="padding: 12px 16px; transition: all 0.3s ease; color: #dc3545;">
                            <i class="fas fa-sign-out-alt mr-3" style="font-size: 1rem;"></i>
                            <span style="font-weight: 500;">Sign Out</span>
                        </a>
                    </div>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Professional Minimalist Sidebar -->
    <aside class="main-sidebar" style="background: #ffffff; border-right: 1px solid #e9ecef; width: 250px; position: fixed; top: 60px; left: 0; bottom: 0; overflow-y: auto; z-index: 1020; box-shadow: 2px 0 10px rgba(0,0,0,0.05);">
        <!-- Navigation Menu -->
        <nav class="mt-3">
            <ul class="nav nav-pills nav-sidebar flex-column" style="padding: 0 15px;">
                <!-- Dashboard - Always visible -->
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_dashboard') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') == 'admin_dashboard' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-tachometer-alt" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Dashboard</span>
                    </a>
                </li>

                <!-- Courses - Permission based -->
                {% set currentAdmin = app.user %}
                {% if currentAdmin.hasPermission('courses.view') or currentAdmin.isMasterAdmin %}
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_courses') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') starts with 'admin_course' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-graduation-cap" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Courses</span>
                    </a>
                </li>
                {% endif %}





                <!-- Trading Education Management Section -->
                <li class="nav-header" style="color: #6c757d; font-size: 0.8rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; padding: 15px 16px 8px; margin-top: 10px;">
                    Trading Education
                </li>

                <!-- Videos Management -->
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_video_index') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') starts with 'admin_video' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-video" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Videos</span>
                    </a>
                </li>

                <!-- Plans Management - Permission based -->
                {% if currentAdmin.hasPermission('plans.view') or currentAdmin.isMasterAdmin %}
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_plans') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') starts with 'admin_plan' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-layer-group" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Plans</span>
                    </a>
                </li>
                {% endif %}

                <!-- Categories Management -->
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_category_index') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') starts with 'admin_category' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-folder" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Categories</span>
                    </a>
                </li>

                <!-- Orders Management -->
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_order_index') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') starts with 'admin_order' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-shopping-cart" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Orders</span>
                    </a>
                </li>

                <!-- User Management Section -->
                <li class="nav-header" style="color: #6c757d; font-size: 0.8rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; padding: 15px 16px 8px; margin-top: 10px;">
                    User Management
                </li>

                <!-- Users - Permission based -->
                {% if currentAdmin.hasPermission('users.view') or currentAdmin.isMasterAdmin %}
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_users') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') starts with 'admin_user' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-users" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Users</span>
                    </a>
                </li>
                {% endif %}

                <!-- Instructors - Permission based -->
                {% if currentAdmin.hasPermission('instructors.view') or currentAdmin.isMasterAdmin %}
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_instructor_index') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') starts with 'admin_instructor' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-chalkboard-teacher" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Instructors</span>
                    </a>
                </li>
                {% endif %}

                <!-- Contacts - Permission based -->
                {% if currentAdmin.hasPermission('contacts.view') or currentAdmin.isMasterAdmin %}
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_contacts') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') starts with 'admin_contact' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-envelope" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Contacts</span>
                    </a>
                </li>
                {% endif %}

                <!-- Market Analysis - Permission based -->
                {% if currentAdmin.hasPermission('market_analysis.view') or currentAdmin.isMasterAdmin %}
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_market_analysis_index') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') starts with 'admin_market_analysis' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-chart-bar" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Market Analysis</span>
                    </a>
                </li>
                {% endif %}

                <!-- Admin Management - Permission based -->
                {% if currentAdmin.hasPermission('admin.view') or currentAdmin.isMasterAdmin %}
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_admins') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') starts with 'admin_admin' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-users-cog" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Admins</span>
                    </a>
                </li>
                {% endif %}



                <!-- Partners Management -->
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_partners') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') starts with 'admin_partners' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-handshake" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Partners</span>
                    </a>
                </li>

                <!-- Promotional Banners - Permission based -->
                {% if currentAdmin.hasPermission('banners.view') or currentAdmin.isMasterAdmin %}
                <li class="nav-item mb-1">
                    <a href="{{ path('admin_promotional_banners') }}"
                       class="nav-link minimal-nav-link {{ app.request.get('_route') starts with 'admin_promotional_banner' ? 'active' : '' }}"
                       style="color: #495057; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-bullhorn" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.9rem;"></i>
                        <span>Banners</span>
                    </a>
                </li>
                {% endif %}

                <!-- Divider -->
                <li class="nav-divider" style="border-top: 1px solid #e9ecef; margin: 16px 8px;"></li>

                <!-- View Site -->
                <li class="nav-item mb-1">
                    <a href="{{ path('app_home') }}"
                       target="_blank"
                       class="nav-link minimal-nav-link"
                       style="color: #6c757d; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; text-decoration: none; display: flex; align-items: center; font-weight: 500;">
                        <i class="fas fa-external-link-alt" style="width: 18px; margin-right: 12px; color: #6c757d; font-size: 0.8rem;"></i>
                        <span>View Site</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Content Header -->
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0">{% block page_title %}Dashboard{% endblock %}</h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            {% block breadcrumbs %}
                            <li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
                            <li class="breadcrumb-item active">Dashboard</li>
                            {% endblock %}
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                {# Flash message display removed for cleanup
                {% for type, messages in app.flashes %}
                    {% for message in messages %}
                        <div class="alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endfor %}
                #}

                {% block content %}{% endblock %}
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="main-footer">
        <strong>Copyright &copy; {{ 'now'|date('Y') }} <a href="{{ path('app_home') }}">Capitol Academy</a>.</strong>
        All rights reserved.
        <div class="float-right d-none d-sm-inline-block">
            <b>Version</b> 1.0.0
        </div>
    </footer>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Bootstrap 5 -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>
<!-- Admin Table Utils -->
<script src="{{ asset('js/admin-table-utils.js') }}"></script>

<!-- Professional Admin Interface JavaScript -->
<script>
$(document).ready(function() {
    // Enhanced Admin dropdown functionality with better Bootstrap 5 compatibility
    const dropdownToggle = document.getElementById('adminDropdownToggle');
    const dropdownMenu = document.querySelector('.dropdown-menu');

    if (dropdownToggle && dropdownMenu) {
        // Initialize Bootstrap dropdown
        const dropdown = new bootstrap.Dropdown(dropdownToggle);

        // Enhanced click handling
        dropdownToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isOpen = dropdownMenu.classList.contains('show');

            if (isOpen) {
                dropdown.hide();
            } else {
                dropdown.show();
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown')) {
                dropdown.hide();
            }
        });

        // Close dropdown when clicking on dropdown items
        dropdownMenu.addEventListener('click', function(e) {
            if (e.target.classList.contains('dropdown-item')) {
                dropdown.hide();
            }
        });
    }

    // Enhanced sidebar navigation
    const navLinks = document.querySelectorAll('.minimal-nav-link');
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(4px)';
        });

        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateX(0)';
            }
        });
    });

    // Responsive sidebar toggle for mobile
    function toggleSidebar() {
        const sidebar = document.querySelector('.main-sidebar');
        const contentWrapper = document.querySelector('.content-wrapper');
        const footer = document.querySelector('.main-footer');

        if (window.innerWidth <= 768) {
            sidebar.style.transform = sidebar.style.transform === 'translateX(0px)' ? 'translateX(-250px)' : 'translateX(0px)';
        }
    }

    // Add mobile menu toggle if needed
    const mobileToggle = document.createElement('button');
    mobileToggle.innerHTML = '<i class="fas fa-bars"></i>';
    mobileToggle.className = 'btn btn-sm btn-outline-light d-md-none';
    mobileToggle.style.cssText = 'position: fixed; top: 15px; left: 15px; z-index: 9999;';
    mobileToggle.addEventListener('click', toggleSidebar);

    if (window.innerWidth <= 768) {
        document.body.appendChild(mobileToggle);
    }

    // Enhanced page loading with professional spinner
    const loadingSpinner = document.getElementById('loading-spinner');

    // Function to hide loading spinner
    function hideLoadingSpinner() {
        if (loadingSpinner && !loadingSpinner.classList.contains('hidden')) {
            loadingSpinner.classList.add('hidden');
            setTimeout(() => {
                if (loadingSpinner) {
                    loadingSpinner.style.display = 'none';
                }
            }, 300);
        }
    }

    // Function to show loading spinner
    function showLoadingSpinner() {
        if (loadingSpinner) {
            loadingSpinner.style.display = 'flex';
            loadingSpinner.classList.remove('hidden');
        }
    }

    // Hide spinner when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', hideLoadingSpinner);
    } else {
        // DOM is already ready, hide immediately
        hideLoadingSpinner();
    }

    // Additional event listeners for various scenarios
    window.addEventListener('load', hideLoadingSpinner);

    // Handle browser back/forward navigation
    window.addEventListener('pageshow', function(e) {
        if (e.persisted) {
            hideLoadingSpinner();
        }
    });

    // Fallback: Force hide spinner after maximum time
    setTimeout(hideLoadingSpinner, 2000);

    // Add fade-in animation to content
    const contentWrapper = document.querySelector('.content-wrapper');
    if (contentWrapper) {
        contentWrapper.classList.add('fade-in-up');
    }

    const sidebar = document.querySelector('.main-sidebar');
    if (sidebar) {
        sidebar.classList.add('slide-in-left');
    }

    // Enhanced form submission with loading states
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Processing...';
            }

            // Show loading overlay for AJAX forms
            if (form.hasAttribute('data-ajax')) {
                showLoadingOverlay();
            }
        });
    });

    // Enhanced AJAX handling
    function showLoadingOverlay(message = 'Processing request...') {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.querySelector('.loading-content p').textContent = message;
            overlay.style.display = 'flex';
        }
    }

    function hideLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    // Enhanced button interactions
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add ripple effect
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });

    // Show spinner for all navigation links (only for internal navigation)
    document.addEventListener('click', function(e) {
        const link = e.target.closest('a');
        if (link &&
            link.href &&
            !link.href.startsWith('#') &&
            !link.href.startsWith('javascript:') &&
            !link.href.startsWith('mailto:') &&
            !link.href.startsWith('tel:') &&
            !link.target &&
            !link.hasAttribute('data-bs-toggle') &&
            !link.classList.contains('no-loading') &&
            !link.classList.contains('dropdown-item')) { // Don't show for dropdown items

            // Only show spinner for internal admin links
            try {
                const linkUrl = new URL(link.href);
                const currentUrl = new URL(window.location.href);
                if (linkUrl.hostname === currentUrl.hostname && linkUrl.pathname.startsWith('/admin')) {
                    showLoadingSpinner();
                }
            } catch (e) {
                // If URL parsing fails, check if it looks like an admin link
                if (link.href.includes('/admin')) {
                    showLoadingSpinner();
                }
            }
        }
    });

    // Show spinner for form submissions (only for admin forms)
    document.addEventListener('submit', function(e) {
        const form = e.target;
        if (form && !form.classList.contains('no-loading') && window.location.pathname.startsWith('/admin')) {
            showLoadingSpinner();
        }
    });

    // Enhanced table interactions
    const tableRows = document.querySelectorAll('table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(30, 60, 114, 0.05)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
    $('#adminDropdownToggle').on('shown.bs.dropdown', function() {
        var $menu = $(this).next('.dropdown-menu');
        $menu.css({
            'position': 'absolute',
            'top': '100%',
            'right': '0',
            'left': 'auto',
            'z-index': '9999'
        });
    });

    // Professional notification system for admin actions - REMOVED
    /*
    window.showAdminNotification = function(message, type = 'success', duration = 5000) {
        // Remove any existing notifications
        $('.admin-notification').remove();

        const iconMap = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-circle',
            'warning': 'fas fa-exclamation-triangle',
            'info': 'fas fa-info-circle'
        };

        const notification = $(`
            <div class="admin-notification alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed"
                 style="top: 80px; right: 20px; z-index: 10000; min-width: 350px; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.2); border: none;">
                <div class="d-flex align-items-center">
                    <i class="${iconMap[type]} me-3" style="font-size: 1.2rem;"></i>
                    <span style="font-weight: 500;">${message}</span>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);

        $('body').append(notification);

        // Auto-remove after specified duration
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, duration);
    };
    */

    console.log('Capitol Academy Professional Admin Interface Loaded');
});
</script>

{% block javascripts %}{% endblock %}

<!-- Standardized Admin Table Components -->
{% block admin_table_modals %}
<!-- Standardized Delete Confirmation Modal -->
<div class="modal fade" id="standardDeleteModal" tabindex="-1" aria-labelledby="standardDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none;">
                <h5 class="modal-title" id="standardDeleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-trash text-danger" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mt-3 mb-2">Are you sure you want to delete this item?</h6>
                <p class="text-muted mb-3"><strong>Warning:</strong> This action cannot be undone.</p>
                <div id="deleteItemDetails" class="bg-light p-3 rounded mb-3"></div>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>Delete
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Standardized Status Toggle Modal -->
<div class="modal fade" id="standardStatusModal" tabindex="-1" aria-labelledby="standardStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; border: none;">
                <h5 class="modal-title" id="standardStatusModalLabel">
                    <i class="fas fa-toggle-on me-2"></i>Change Status
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-exchange-alt text-primary" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mt-3 mb-2" id="statusChangeMessage">Change item status?</h6>
                <div id="statusItemDetails" class="bg-light p-3 rounded mb-3"></div>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" id="confirmStatusBtn">
                    <i class="fas fa-check me-2"></i>Confirm
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- Professional Permission Denied Modal -->
<div class="modal fade" id="permissionDeniedModal" tabindex="-1" aria-labelledby="permissionDeniedModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header border-0" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white;">
                <h5 class="modal-title fw-bold" id="permissionDeniedModalLabel">
                    <i class="fas fa-shield-alt me-2"></i>Access Denied
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-4">
                    <i class="fas fa-lock text-danger" style="font-size: 3rem;"></i>
                </div>
                <h6 class="fw-bold mb-3">Permission Required</h6>
                <p class="text-muted mb-4" id="permissionDeniedMessage">
                    You do not have permission to access this feature. Please contact your administrator.
                </p>
                <div class="d-flex gap-2 justify-content-center">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Close
                    </button>
                    <a href="{{ path('admin_dashboard') }}" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>Go to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Professional permission denied handler
window.showPermissionDeniedModal = function(message = 'You do not have permission to access this feature. Please contact your administrator.') {
    document.getElementById('permissionDeniedMessage').textContent = message;
    const modal = new bootstrap.Modal(document.getElementById('permissionDeniedModal'));
    modal.show();
};

// Handle permission denied responses from AJAX calls
$(document).ajaxError(function(event, xhr, settings) {
    if (xhr.status === 403) {
        const response = xhr.responseJSON;
        const message = response && response.message ? response.message : 'You do not have permission to access this feature. Please contact your administrator.';
        showPermissionDeniedModal(message);
    }
});

// Standardized Admin Table Functions
window.AdminTableUtils = {
    // Standardized search functionality
    initializeSearch: function(searchInputId, tableRowClass, searchFields) {
        let searchTimeout;
        const searchInput = $(searchInputId);
        const searchClearBtn = searchInput.siblings('.input-group-append').find('button');
        const resultsCount = $('#search-results-count');

        searchInput.on('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = $(this).val().toLowerCase().trim();

            searchTimeout = setTimeout(function() {
                AdminTableUtils.performSearch(searchTerm, tableRowClass, searchFields, resultsCount);
            }, 300);
        });

        searchClearBtn.on('click', function() {
            if (searchInput.val()) {
                searchInput.val('');
                AdminTableUtils.performSearch('', tableRowClass, searchFields, resultsCount);
                searchInput.focus();
            }
        });
    },

    performSearch: function(searchTerm, tableRowClass, searchFields, resultsCount) {
        let totalCount = 0;
        let visibleCount = 0;

        $(tableRowClass).each(function() {
            totalCount++;
            let isMatch = false;

            if (searchTerm === '') {
                isMatch = true;
            } else {
                for (let field of searchFields) {
                    const fieldValue = $(this).find(field).text().toLowerCase();
                    if (fieldValue.includes(searchTerm)) {
                        isMatch = true;
                        break;
                    }
                }
            }

            if (isMatch) {
                $(this).show().addClass('search-match');
                visibleCount++;
            } else {
                $(this).hide().removeClass('search-match');
            }
        });

        // Update results count
        if (resultsCount.length) {
            if (searchTerm === '') {
                resultsCount.hide();
            } else {
                resultsCount.show().text(`Showing ${visibleCount} of ${totalCount} results`);
            }
        }
    },

    // Standardized delete confirmation
    showDeleteModal: function(itemName, itemDetails, deleteCallback) {
        const modal = new bootstrap.Modal(document.getElementById('standardDeleteModal'));
        document.getElementById('deleteItemDetails').innerHTML = `
            <strong>${itemName}</strong><br>
            <small class="text-muted">${itemDetails}</small>
        `;

        const confirmBtn = document.getElementById('confirmDeleteBtn');
        confirmBtn.onclick = function() {
            deleteCallback();
            modal.hide();
        };

        modal.show();
    },

    // Standardized status toggle
    showStatusModal: function(itemName, currentStatus, statusCallback) {
        const modal = new bootstrap.Modal(document.getElementById('standardStatusModal'));
        const newStatus = currentStatus ? 'deactivate' : 'activate';

        document.getElementById('statusChangeMessage').textContent = `Do you want to ${newStatus} this item?`;
        document.getElementById('statusItemDetails').innerHTML = `
            <strong>${itemName}</strong><br>
            <small class="text-muted">Current status: ${currentStatus ? 'Active' : 'Inactive'}</small>
        `;

        const confirmBtn = document.getElementById('confirmStatusBtn');
        confirmBtn.onclick = function() {
            statusCallback();
            modal.hide();
        };

        modal.show();
    },

    // Standardized action button styling
    getActionButtonHtml: function(buttons) {
        let html = '<div class="btn-group" role="group">';

        buttons.forEach(button => {
            const styles = {
                view: 'background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white;',
                edit: 'background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #343a40;',
                delete: 'background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white;',
                toggle: 'background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;'
            };

            html += `<a href="${button.url || 'javascript:void(0);'}"
                       class="btn btn-sm shadow-sm"
                       style="${styles[button.type] || styles.view} border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 0.25rem; transition: all 0.3s ease;"
                       title="${button.title}"
                       ${button.onclick ? `onclick="${button.onclick}"` : ''}>
                        <i class="${button.icon}"></i>
                     </a>`;
        });

        html += '</div>';
        return html;
    }
};
</script>
</body>
</html>
