<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/courses/preview.html.twig */
class __TwigTemplate_a90e7d494db4a41efee8198addf25f85 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/preview.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/preview.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Course Details - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Course Details";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses");
        yield "\">Courses</a></li>
<li class=\"breadcrumb-item active\">";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 10, $this->source); })()), "title", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Course Details: ";
        // line 37
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 37, $this->source); })()), "code", [], "any", false, false, false, 37), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Course Button (Icon Only) -->
                        <a href=\"";
        // line 43
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_course_edit", ["code" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 43, $this->source); })()), "code", [], "any", false, false, false, 43)]), "html", null, true);
        yield "\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit Course\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Print Course Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Course Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Back to Courses Button -->
                        <a href=\"";
        // line 63
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">

                        <!-- Course Code and Title Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Course Code -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                        Course Code
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 91
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 91, $this->source); })()), "code", [], "any", false, false, false, 91), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
                                        Course Title
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 104
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 104, $this->source); })()), "title", [], "any", false, false, false, 104), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Description -->
                        <div class=\"row print-full-width clearfix\">
                            <div class=\"col-12\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                        Course Description
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 100px;\">
                                        ";
        // line 119
        yield Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 119, $this->source); })()), "description", [], "any", false, false, false, 119), "html", null, true));
        yield "
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category and Level Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Category -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-tags text-primary mr-1\"></i>
                                        Category
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 135
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 135, $this->source); })()), "category", [], "any", false, false, false, 135)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 136
            yield "                                            ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 136, $this->source); })()), "category", [], "any", false, false, false, 136), "html", null, true);
            yield "
                                        ";
        } else {
            // line 138
            yield "                                            No category assigned
                                        ";
        }
        // line 140
        yield "                                    </div>
                                </div>
                            </div>

                            <!-- Level -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-signal text-primary mr-1\"></i>
                                        Level
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 152
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 152, $this->source); })()), "level", [], "any", false, false, false, 152)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 153
            yield "                                            ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 153, $this->source); })()), "level", [], "any", false, false, false, 153)), "html", null, true);
            yield "
                                        ";
        } else {
            // line 155
            yield "                                            Not specified
                                        ";
        }
        // line 157
        yield "                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Details Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Course Status -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                        Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 172
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 172, $this->source); })()), "isActive", [], "any", false, false, false, 172)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Active") : ("Inactive"));
        yield "
                                    </div>
                                </div>
                            </div>

                            <!-- View Count -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-eye text-primary mr-1\"></i>
                                        View Count
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 185
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((CoreExtension::getAttribute($this->env, $this->source, ($context["course"] ?? null), "viewCount", [], "any", true, true, false, 185)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 185, $this->source); })()), "viewCount", [], "any", false, false, false, 185), 0)) : (0)), "html", null, true);
        yield " views
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Learning Outcomes -->
                        ";
        // line 192
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 192, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 192) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 192, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 192)) > 0))) {
            // line 193
            yield "                        <div class=\"row print-full-width clearfix\">
                            <div class=\"col-12\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-bullseye text-primary mr-1\"></i>
                                        Learning Outcomes
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        <ul class=\"mb-0\">
                                            ";
            // line 202
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 202, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 202));
            foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                // line 203
                yield "                                                <li>";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                yield "</li>
                                            ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 205
            yield "                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        ";
        }
        // line 211
        yield "
                        <!-- Course Features -->
                        ";
        // line 213
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 213, $this->source); })()), "features", [], "any", false, false, false, 213) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 213, $this->source); })()), "features", [], "any", false, false, false, 213)) > 0))) {
            // line 214
            yield "                        <div class=\"row print-full-width clearfix\">
                            <div class=\"col-12\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-star text-primary mr-1\"></i>
                                        Course Features
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        <ul class=\"mb-0\">
                                            ";
            // line 223
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 223, $this->source); })()), "features", [], "any", false, false, false, 223));
            foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                // line 224
                yield "                                                <li>";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                yield "</li>
                                            ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 226
            yield "                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        ";
        }
        // line 232
        yield "
                        <!-- Course Modules -->
                        ";
        // line 234
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 234, $this->source); })()), "modules", [], "any", false, false, false, 234) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 234, $this->source); })()), "modules", [], "any", false, false, false, 234)) > 0))) {
            // line 235
            yield "                        <div class=\"row print-full-width clearfix\">
                            <div class=\"col-12\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-list text-primary mr-1\"></i>
                                        Course Modules (";
            // line 240
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 240, $this->source); })()), "modules", [], "any", false, false, false, 240)), "html", null, true);
            yield ")
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem;\">
                                        ";
            // line 243
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 243, $this->source); })()), "modules", [], "any", false, false, false, 243));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["module"]) {
                // line 244
                yield "                                        <div class=\"module-item mb-3 p-3\" style=\"background: white; border: 1px solid #dee2e6; border-radius: 8px;\">
                                            <!-- Module Header -->
                                            <div class=\"row\">
                                                <div class=\"col-md-6\">
                                                    <h6 class=\"mb-1\" style=\"color: #011a2d; font-weight: 600;\">
                                                        <i class=\"fas fa-play-circle text-primary mr-2\"></i>
                                                        ";
                // line 250
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "title", [], "any", false, false, false, 250), "html", null, true);
                yield "
                                                    </h6>
                                                    <small class=\"text-muted\">Code: ";
                // line 252
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "code", [], "any", false, false, false, 252), "html", null, true);
                yield "</small>
                                                </div>
                                                <div class=\"col-md-6 text-md-right\">
                                                    <span class=\"badge bg-info\">Module ";
                // line 255
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 255), "html", null, true);
                yield "</span>
                                                    <span class=\"badge bg-success\">";
                // line 256
                yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["module"], "isActive", [], "any", false, false, false, 256)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Active") : ("Inactive"));
                yield "</span>
                                                </div>
                                            </div>

                                            <!-- Module Description -->
                                            ";
                // line 261
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 261)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 262
                    yield "                                            <div class=\"mt-3\">
                                                <h6 class=\"mb-2\" style=\"color: #011a2d; font-weight: 600; font-size: 0.9rem;\">
                                                    <i class=\"fas fa-align-left text-primary mr-2\"></i>
                                                    Description
                                                </h6>
                                                <p class=\"mb-0 text-muted\" style=\"font-size: 0.9rem; line-height: 1.5;\">";
                    // line 267
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 267), "html", null, true);
                    yield "</p>
                                            </div>
                                            ";
                }
                // line 270
                yield "
                                            <!-- Module Learning Outcomes -->
                                            ";
                // line 272
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 272) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 272)) > 0))) {
                    // line 273
                    yield "                                            <div class=\"mt-3\">
                                                <h6 class=\"mb-2\" style=\"color: #011a2d; font-weight: 600; font-size: 0.9rem;\">
                                                    <i class=\"fas fa-graduation-cap text-primary mr-2\"></i>
                                                    Learning Outcomes
                                                </h6>
                                                <ul class=\"mb-0\" style=\"font-size: 0.9rem; line-height: 1.5; padding-left: 1.2rem;\">
                                                    ";
                    // line 279
                    $context['_parent'] = $context;
                    $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 279));
                    foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                        // line 280
                        yield "                                                    <li class=\"text-muted mb-1\">";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                        yield "</li>
                                                    ";
                    }
                    $_parent = $context['_parent'];
                    unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
                    $context = array_intersect_key($context, $_parent) + $_parent;
                    // line 282
                    yield "                                                </ul>
                                            </div>
                                            ";
                }
                // line 285
                yield "
                                            <!-- Module Features -->
                                            ";
                // line 287
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 287) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 287)) > 0))) {
                    // line 288
                    yield "                                            <div class=\"mt-3\">
                                                <h6 class=\"mb-2\" style=\"color: #011a2d; font-weight: 600; font-size: 0.9rem;\">
                                                    <i class=\"fas fa-star text-primary mr-2\"></i>
                                                    Features
                                                </h6>
                                                <ul class=\"mb-0\" style=\"font-size: 0.9rem; line-height: 1.5; padding-left: 1.2rem;\">
                                                    ";
                    // line 294
                    $context['_parent'] = $context;
                    $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 294));
                    foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                        // line 295
                        yield "                                                    <li class=\"text-muted mb-1\">";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                        yield "</li>
                                                    ";
                    }
                    $_parent = $context['_parent'];
                    unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent']);
                    $context = array_intersect_key($context, $_parent) + $_parent;
                    // line 297
                    yield "                                                </ul>
                                            </div>
                                            ";
                }
                // line 300
                yield "                                        </div>
                                        ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['module'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 302
            yield "                                    </div>
                                </div>
                            </div>
                        </div>
                        ";
        }
        // line 307
        yield "
                        <!-- Course Images -->
                        <div class=\"row print-full-width clearfix\">
                            <!-- Thumbnail Image -->
                            <div class=\"col-12\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-image text-primary mr-1\"></i>
                                        Thumbnail Image
                                    </label>
                                    <div class=\"enhanced-display-field text-center\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem;\">
                                        ";
        // line 318
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 318, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 318)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 319
            yield "                                            <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getFunction('course_image_url')->getCallable()(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 319, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 319), "thumbnail"), "html", null, true);
            yield "\"
                                                 alt=\"Course Thumbnail\"
                                                 class=\"img-fluid rounded mx-auto d-block\"
                                                 style=\"height: 200px; width: 300px; object-fit: cover;\">
                                        ";
        } else {
            // line 324
            yield "                                            <div class=\"text-center text-muted py-3\">
                                                <i class=\"fas fa-image fa-3x mb-2\"></i>
                                                <p class=\"mb-0\">No thumbnail image</p>
                                            </div>
                                        ";
        }
        // line 329
        yield "                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Status and Dates -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Status -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                        Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 344
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 344, $this->source); })()), "isActive", [], "any", false, false, false, 344)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 345
            yield "                                            <span class=\"badge bg-success\">Active</span>
                                        ";
        } else {
            // line 347
            yield "                                            <span class=\"badge bg-secondary\">Inactive</span>
                                        ";
        }
        // line 349
        yield "                                    </div>
                                </div>
                            </div>

                            <!-- Created Date -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                        Created Date
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        ";
        // line 361
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 361, $this->source); })()), "createdAt", [], "any", false, false, false, 361), "F j, Y g:i A"), "html", null, true);
        yield "
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
        </div>
    </div>
</div>

<style>
/* Print Styles */
@media print {
    .btn, .breadcrumb, .alert {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .card-header {
        background: #011a2d !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
    }

    .enhanced-display-field {
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        -webkit-print-color-adjust: exact;
    }

    .print-two-column {
        page-break-inside: avoid;
    }

    .print-full-width {
        page-break-inside: avoid;
    }

    .module-item {
        page-break-inside: avoid;
        background: white !important;
        border: 1px solid #dee2e6 !important;
        -webkit-print-color-adjust: exact;
    }
}

/* Enhanced Display Field Styling */
.enhanced-display-field {
    font-weight: 500;
    line-height: 1.5;
}

.enhanced-display-field ul {
    padding-left: 1.5rem;
}

.enhanced-display-field ul li {
    margin-bottom: 0.25rem;
}

.module-item {
    transition: all 0.3s ease;
}

.module-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/courses/preview.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  720 => 361,  706 => 349,  702 => 347,  698 => 345,  696 => 344,  679 => 329,  672 => 324,  663 => 319,  661 => 318,  648 => 307,  641 => 302,  626 => 300,  621 => 297,  612 => 295,  608 => 294,  600 => 288,  598 => 287,  594 => 285,  589 => 282,  580 => 280,  576 => 279,  568 => 273,  566 => 272,  562 => 270,  556 => 267,  549 => 262,  547 => 261,  539 => 256,  535 => 255,  529 => 252,  524 => 250,  516 => 244,  499 => 243,  493 => 240,  486 => 235,  484 => 234,  480 => 232,  472 => 226,  463 => 224,  459 => 223,  448 => 214,  446 => 213,  442 => 211,  434 => 205,  425 => 203,  421 => 202,  410 => 193,  408 => 192,  398 => 185,  382 => 172,  365 => 157,  361 => 155,  355 => 153,  353 => 152,  339 => 140,  335 => 138,  329 => 136,  327 => 135,  308 => 119,  290 => 104,  274 => 91,  243 => 63,  220 => 43,  211 => 37,  201 => 29,  191 => 25,  188 => 24,  184 => 23,  181 => 22,  171 => 18,  168 => 17,  164 => 16,  160 => 14,  147 => 13,  134 => 10,  130 => 9,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Course Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Course Details{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_courses') }}\">Courses</a></li>
<li class=\"breadcrumb-item active\">{{ course.title }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Course Details: {{ course.code }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Course Button (Icon Only) -->
                        <a href=\"{{ path('admin_course_edit', {'code': course.code}) }}\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit Course\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Print Course Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Course Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Back to Courses Button -->
                        <a href=\"{{ path('admin_courses') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
                <!-- Single Column Layout -->
                <div class=\"row\">
                    <div class=\"col-12\">

                        <!-- Course Code and Title Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Course Code -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                        Course Code
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {{ course.code }}
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
                                        Course Title
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {{ course.title }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Description -->
                        <div class=\"row print-full-width clearfix\">
                            <div class=\"col-12\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                        Course Description
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 100px;\">
                                        {{ course.description|nl2br }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category and Level Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Category -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-tags text-primary mr-1\"></i>
                                        Category
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {% if course.category %}
                                            {{ course.category }}
                                        {% else %}
                                            No category assigned
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Level -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-signal text-primary mr-1\"></i>
                                        Level
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {% if course.level %}
                                            {{ course.level|title }}
                                        {% else %}
                                            Not specified
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Details Row -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Course Status -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                        Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {{ course.isActive ? 'Active' : 'Inactive' }}
                                    </div>
                                </div>
                            </div>

                            <!-- View Count -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-eye text-primary mr-1\"></i>
                                        View Count
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {{ course.viewCount|default(0) }} views
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Learning Outcomes -->
                        {% if course.learningOutcomes and course.learningOutcomes|length > 0 %}
                        <div class=\"row print-full-width clearfix\">
                            <div class=\"col-12\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-bullseye text-primary mr-1\"></i>
                                        Learning Outcomes
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        <ul class=\"mb-0\">
                                            {% for outcome in course.learningOutcomes %}
                                                <li>{{ outcome }}</li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Course Features -->
                        {% if course.features and course.features|length > 0 %}
                        <div class=\"row print-full-width clearfix\">
                            <div class=\"col-12\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-star text-primary mr-1\"></i>
                                        Course Features
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        <ul class=\"mb-0\">
                                            {% for feature in course.features %}
                                                <li>{{ feature }}</li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Course Modules -->
                        {% if course.modules and course.modules|length > 0 %}
                        <div class=\"row print-full-width clearfix\">
                            <div class=\"col-12\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-list text-primary mr-1\"></i>
                                        Course Modules ({{ course.modules|length }})
                                    </label>
                                    <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem;\">
                                        {% for module in course.modules %}
                                        <div class=\"module-item mb-3 p-3\" style=\"background: white; border: 1px solid #dee2e6; border-radius: 8px;\">
                                            <!-- Module Header -->
                                            <div class=\"row\">
                                                <div class=\"col-md-6\">
                                                    <h6 class=\"mb-1\" style=\"color: #011a2d; font-weight: 600;\">
                                                        <i class=\"fas fa-play-circle text-primary mr-2\"></i>
                                                        {{ module.title }}
                                                    </h6>
                                                    <small class=\"text-muted\">Code: {{ module.code }}</small>
                                                </div>
                                                <div class=\"col-md-6 text-md-right\">
                                                    <span class=\"badge bg-info\">Module {{ loop.index }}</span>
                                                    <span class=\"badge bg-success\">{{ module.isActive ? 'Active' : 'Inactive' }}</span>
                                                </div>
                                            </div>

                                            <!-- Module Description -->
                                            {% if module.description %}
                                            <div class=\"mt-3\">
                                                <h6 class=\"mb-2\" style=\"color: #011a2d; font-weight: 600; font-size: 0.9rem;\">
                                                    <i class=\"fas fa-align-left text-primary mr-2\"></i>
                                                    Description
                                                </h6>
                                                <p class=\"mb-0 text-muted\" style=\"font-size: 0.9rem; line-height: 1.5;\">{{ module.description }}</p>
                                            </div>
                                            {% endif %}

                                            <!-- Module Learning Outcomes -->
                                            {% if module.learningOutcomes and module.learningOutcomes|length > 0 %}
                                            <div class=\"mt-3\">
                                                <h6 class=\"mb-2\" style=\"color: #011a2d; font-weight: 600; font-size: 0.9rem;\">
                                                    <i class=\"fas fa-graduation-cap text-primary mr-2\"></i>
                                                    Learning Outcomes
                                                </h6>
                                                <ul class=\"mb-0\" style=\"font-size: 0.9rem; line-height: 1.5; padding-left: 1.2rem;\">
                                                    {% for outcome in module.learningOutcomes %}
                                                    <li class=\"text-muted mb-1\">{{ outcome }}</li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                            {% endif %}

                                            <!-- Module Features -->
                                            {% if module.features and module.features|length > 0 %}
                                            <div class=\"mt-3\">
                                                <h6 class=\"mb-2\" style=\"color: #011a2d; font-weight: 600; font-size: 0.9rem;\">
                                                    <i class=\"fas fa-star text-primary mr-2\"></i>
                                                    Features
                                                </h6>
                                                <ul class=\"mb-0\" style=\"font-size: 0.9rem; line-height: 1.5; padding-left: 1.2rem;\">
                                                    {% for feature in module.features %}
                                                    <li class=\"text-muted mb-1\">{{ feature }}</li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Course Images -->
                        <div class=\"row print-full-width clearfix\">
                            <!-- Thumbnail Image -->
                            <div class=\"col-12\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-image text-primary mr-1\"></i>
                                        Thumbnail Image
                                    </label>
                                    <div class=\"enhanced-display-field text-center\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem;\">
                                        {% if course.thumbnailImage %}
                                            <img src=\"{{ course_image_url(course.thumbnailImage, 'thumbnail') }}\"
                                                 alt=\"Course Thumbnail\"
                                                 class=\"img-fluid rounded mx-auto d-block\"
                                                 style=\"height: 200px; width: 300px; object-fit: cover;\">
                                        {% else %}
                                            <div class=\"text-center text-muted py-3\">
                                                <i class=\"fas fa-image fa-3x mb-2\"></i>
                                                <p class=\"mb-0\">No thumbnail image</p>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Status and Dates -->
                        <div class=\"row print-two-column clearfix\">
                            <!-- Status -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                        Status
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {% if course.isActive %}
                                            <span class=\"badge bg-success\">Active</span>
                                        {% else %}
                                            <span class=\"badge bg-secondary\">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Created Date -->
                            <div class=\"col-md-6\">
                                <div class=\"form-group\">
                                    <label class=\"form-label\">
                                        <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                        Created Date
                                    </label>
                                    <div class=\"form-control-plaintext enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                        {{ course.createdAt|date('F j, Y g:i A') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
        </div>
    </div>
</div>

<style>
/* Print Styles */
@media print {
    .btn, .breadcrumb, .alert {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .card-header {
        background: #011a2d !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
    }

    .enhanced-display-field {
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        -webkit-print-color-adjust: exact;
    }

    .print-two-column {
        page-break-inside: avoid;
    }

    .print-full-width {
        page-break-inside: avoid;
    }

    .module-item {
        page-break-inside: avoid;
        background: white !important;
        border: 1px solid #dee2e6 !important;
        -webkit-print-color-adjust: exact;
    }
}

/* Enhanced Display Field Styling */
.enhanced-display-field {
    font-weight: 500;
    line-height: 1.5;
}

.enhanced-display-field ul {
    padding-left: 1.5rem;
}

.enhanced-display-field ul li {
    margin-bottom: 0.25rem;
}

.module-item {
    transition: all 0.3s ease;
}

.module-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
{% endblock %}
", "admin/courses/preview.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\courses\\preview.html.twig");
    }
}
