{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Contact',
    'entity_title': contact.fullName,
    'entity_code': contact.fullName,
    'entity_icon': 'fas fa-envelope',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_contacts', 'title': 'Contacts'},
        {'title': contact.fullName, 'active': true}
    ],
    'back_path': path('admin_contacts'),
    'print_function': 'printContactDetails'
} %}

{% block preview_content %}
    <!-- Contact Information Row -->
    <div class="row">
        <!-- Full Name -->
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-user text-primary mr-1"></i>
                    Full Name
                </label>
                <div class="enhanced-display-field" style="font-weight: normal;">
                    {{ contact.fullName }}
                </div>
            </div>
        </div>

        <!-- Email Address -->
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-envelope text-primary mr-1"></i>
                    Email Address
                </label>
                <div class="enhanced-display-field" style="font-weight: normal;">
                    <a href="mailto:{{ contact.email }}" class="text-decoration-none" style="color: #011a2d;">{{ contact.email }}</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Subject and Source Row -->
    <div class="row">
        <!-- Subject -->
        <div class="col-md-8">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-tag text-primary mr-1"></i>
                    Subject
                </label>
                <div class="enhanced-display-field" style="font-weight: normal;">
                    {{ contact.subject ?? 'No subject provided' }}
                </div>
            </div>
        </div>

        <!-- Source Page -->
        <div class="col-md-4">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-link text-primary mr-1"></i>
                    Source Page
                </label>
                <div class="enhanced-display-field" style="font-weight: normal;">
                    {{ contact.sourcePage ?? 'Unknown' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Message -->
    <div class="form-group">
        <label class="form-label">
            <i class="fas fa-comment text-primary mr-1"></i>
            Message
        </label>
        <div class="enhanced-display-field" style="font-weight: normal; line-height: 1.6; min-height: 120px;">
            {{ contact.message ? contact.message|nl2br : 'No message provided' }}
        </div>
    </div>

    <!-- IP Address and Country Row -->
    <div class="row">
        <!-- IP Address -->
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-map-marker-alt text-primary mr-1"></i>
                    IP Address
                </label>
                <div class="enhanced-display-field" style="font-weight: normal;">
                    {{ contact.ipAddress ?? 'Not recorded' }}
                </div>
            </div>
        </div>

        <!-- Country -->
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-globe text-primary mr-1"></i>
                    Country
                </label>
                <div class="enhanced-display-field" style="font-weight: normal;">
                    {{ contact.country ?? 'Not specified' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Date and Status Row -->
    <div class="row">
        <!-- Contact Date -->
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-calendar text-primary mr-1"></i>
                    Contact Date
                </label>
                <div class="enhanced-display-field" style="font-weight: normal;">
                    {{ contact.createdAt|date('F j, Y \\a\\t g:i A') }}
                </div>
            </div>
        </div>

        <!-- Status -->
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-toggle-on text-primary mr-1"></i>
                    Status
                </label>
                <div class="enhanced-display-field" style="font-weight: normal;">
                    {% if contact.processed %}
                        <span class="badge bg-success">Processed</span>
                    {% else %}
                        <span class="badge bg-warning">Pending</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% endembed %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Toggle processed status functionality
    $('.toggle-processed-btn').on('click', function() {
        const button = $(this);
        const contactSlug = button.data('contact-slug');
        const originalHtml = button.html();

        // Show loading state
        button.prop('disabled', true);
        button.html('<i class="fas fa-spinner fa-spin me-1"></i>Processing...');

        // Make AJAX request
        $.ajax({
            url: '{{ path('admin_contact_toggle_processed', {'slug': 'PLACEHOLDER'}) }}'.replace('PLACEHOLDER', contactSlug),
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            success: function(response) {
                if (response.success) {
                    // Remove success message - just reload the page
                    setTimeout(function() {
                        location.reload();
                    }, 500);
                } else {
                    showAlert('danger', response.message || 'An error occurred');
                    button.prop('disabled', false);
                    button.html(originalHtml);
                }
            },
            error: function(xhr) {
                let errorMessage = 'An error occurred while updating the contact status.';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 403) {
                    errorMessage = 'You do not have permission to perform this action.';
                } else if (xhr.status === 404) {
                    errorMessage = 'Contact not found.';
                }

                showAlert('danger', errorMessage);
                button.prop('disabled', false);
                button.html(originalHtml);
            }
        });
    });

    // Function to show Capitol Academy styled alert messages - REMOVED
    /*
    function showAlert(type, message) {
        const alertStyle = type === 'success' ?
            'background: #011a2d; color: white; border: 1px solid #011a2d;' :
            'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;';

        const alertHtml = `
            <div class="alert alert-dismissible fade show" role="alert" style="${alertStyle} border-radius: 8px; margin-bottom: 1rem;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close ${type === 'success' ? 'btn-close-white' : ''}" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // Remove existing alerts to prevent duplicates
        $('.alert').remove();

        // Add new alert above the content
        $('.container-fluid').prepend(alertHtml);

        // Auto-dismiss after 2 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 2000);
    }
    */

    // Print functionality enhancement
    window.addEventListener('beforeprint', function() {
        // Hide action buttons when printing
        $('.action-buttons').hide();
    });

    window.addEventListener('afterprint', function() {
        // Show action buttons after printing
        $('.action-buttons').show();
    });
});

// Print function for the preview layout
function printContactDetails() {
    window.print();
}
</script>
{% endblock %}
