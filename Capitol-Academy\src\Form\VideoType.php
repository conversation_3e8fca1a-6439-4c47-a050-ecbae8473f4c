<?php

namespace App\Form;

use App\Entity\Video;
use App\Repository\CategoryRepository;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\UrlType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\GreaterThanOrEqual;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class VideoType extends AbstractType
{
    public function __construct(
        private CategoryRepository $categoryRepository
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Video Title',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter video title',
                    'maxlength' => '255'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Title is required']),
                    new Length(['max' => 255, 'maxMessage' => 'Title cannot be longer than 255 characters'])
                ]
            ])
            ->add('category', ChoiceType::class, [
                'label' => 'Category',
                'choices' => $this->getCategoryChoices(),
                'attr' => [
                    'class' => 'form-select'
                ],
                'required' => true,
                'placeholder' => 'Select Category'
            ])
            ->add('videoSourceType', ChoiceType::class, [
                'label' => 'Video Source Type',
                'choices' => [
                    'YouTube Video' => 'youtube',
                    'VdoCipher (DRM Protected)' => 'vdocipher',
                    'Direct Upload' => 'upload'
                ],
                'attr' => [
                    'class' => 'form-select',
                    'data-video-source-toggle' => 'true'
                ],
                'required' => true,
                'help' => 'Choose how you want to provide the video content'
            ])
            ->add('youtubeUrl', UrlType::class, [
                'label' => 'YouTube URL',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'https://www.youtube.com/watch?v=...',
                    'data-video-source' => 'youtube'
                ],
                'help' => 'Enter the full YouTube video URL'
            ])
            ->add('vdocipherVideoId', TextType::class, [
                'label' => 'VdoCipher Video ID',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter VdoCipher video ID',
                    'data-video-source' => 'vdocipher'
                ],
                'help' => 'Enter the VdoCipher video ID for DRM-protected streaming'
            ])
            ->add('accessLevel', ChoiceType::class, [
                'label' => 'Access Level',
                'choices' => [
                    'Public Free (No login required)' => 'public_free',
                    'Login Required Free (Registered users only)' => 'login_required_free',
                    'Premium (Paid access)' => 'premium'
                ],
                'attr' => [
                    'class' => 'form-select',
                    'data-access-level-toggle' => 'true'
                ],
                'required' => true,
                'help' => 'Choose who can access this video'
            ])
            ->add('isFree', CheckboxType::class, [
                'label' => 'Legacy Free Video Flag',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input',
                    'style' => 'display: none;' // Hidden for backward compatibility
                ],
                'help' => 'Maintained for backward compatibility'
            ])
            ->add('price', MoneyType::class, [
                'label' => 'Price',
                'currency' => 'USD',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => '0.00',
                    'data-access-level' => 'premium'
                ],
                'constraints' => [
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Price must be greater than or equal to 0'])
                ],
                'help' => 'Set price for premium videos'
            ])
            ->add('accessDuration', IntegerType::class, [
                'label' => 'Access Duration (Days)',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Leave empty for lifetime access',
                    'min' => 1,
                    'data-access-level' => 'premium'
                ],
                'constraints' => [
                    new GreaterThanOrEqual(['value' => 1, 'message' => 'Access duration must be at least 1 day'])
                ],
                'help' => 'Number of days user will have access after purchase (leave empty for lifetime access)'
            ])

            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 4,
                    'placeholder' => 'Enter video description'
                ],
                'constraints' => [
                    new Length(['max' => 2000, 'maxMessage' => 'Description cannot be longer than 2000 characters'])
                ]
            ])
            ->add('thumbnailFile', FileType::class, [
                'label' => 'Thumbnail Image',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*'
                ],
                'constraints' => [
                    new File([
                        'maxSize' => '5M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPEG, PNG, WebP)',
                        'maxSizeMessage' => 'The file is too large. Maximum size is 5MB.'
                    ])
                ],
                'help' => 'Upload a thumbnail image (JPEG, PNG, WebP). Max size: 5MB'
            ])
            ->add('videoFile', FileType::class, [
                'label' => 'Video File',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'video/*',
                    'data-video-source' => 'upload'
                ],
                'constraints' => [
                    new File([
                        'maxSize' => '500M',
                        'mimeTypes' => [
                            'video/mp4',
                            'video/avi',
                            'video/mov',
                            'video/wmv',
                            'video/webm'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid video file (MP4, AVI, MOV, WMV, WebM)',
                        'maxSizeMessage' => 'The file is too large. Maximum size is 500MB.'
                    ])
                ],
                'help' => 'Upload a video file (MP4, AVI, MOV, WMV, WebM). Max size: 500MB'
            ])

;
    }

    private function getCategoryChoices(): array
    {
        try {
            $categories = $this->categoryRepository->findForVideos();
            $choices = [];

            foreach ($categories as $category) {
                $choices[$category->getName()] = $category->getName();
            }

            return $choices;
        } catch (\Exception $e) {
            // Fallback in case of database error
            return [
                'Technical Analysis' => 'Technical Analysis',
                'Market Analysis' => 'Market Analysis',
                'Trading Strategies' => 'Trading Strategies',
                'Live Trading' => 'Live Trading'
            ];
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Video::class,
        ]);
    }
}
